"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "delays", {
  enumerable: true,
  get: function () {
    return _times.delays;
  }
});
Object.defineProperty(exports, "loops", {
  enumerable: true,
  get: function () {
    return _times.loops;
  }
});
Object.defineProperty(exports, "speeds", {
  enumerable: true,
  get: function () {
    return _times.speeds;
  }
});
exports.types = void 0;
var _times = require("./src/constants/times");
var types = _interopRequireWildcard(require("./src/constants/types"));
exports.types = types;
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }