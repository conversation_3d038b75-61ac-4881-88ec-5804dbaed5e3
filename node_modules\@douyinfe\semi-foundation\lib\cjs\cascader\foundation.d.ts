import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface BasicData {
    data: BasicCascaderData;
    disabled: boolean;
    key: string;
    searchText: any[];
    pathData?: BasicCascaderData[];
}
export interface Virtualize {
    itemSize: number;
    height?: number | string;
    width?: number | string;
}
export interface BasicEntities {
    [idx: string]: BasicEntity;
}
export interface BasicEntity {
    _notExist?: boolean;
    children?: Array<BasicEntity>;
    data: BasicCascaderData;
    ind: number;
    key: string;
    level: number;
    parent?: BasicEntity;
    parentKey?: string;
    path: Array<string>;
    pos: string;
    valuePath: Array<string>;
}
export interface BasicCascaderData {
    [x: string]: any;
    value: string | number;
    label: any;
    disabled?: boolean;
    isLeaf?: boolean;
    loading?: boolean;
    children?: BasicCascaderData[];
}
export type CascaderType = 'large' | 'small' | 'default';
export type BasicSimpleValueType = string | number | BasicCascaderData;
export type BasicValue = BasicSimpleValueType | Array<BasicSimpleValueType> | Array<Array<BasicSimpleValueType>>;
export type ShowNextType = 'click' | 'hover';
export interface BasicTriggerRenderProps {
    componentProps: BasicCascaderProps;
    disabled: boolean;
    /** The hierarchical position of the selected node in treeData,
     * as in the following example, when Zhejiang-Hangzhou-Xiaoshan
     * District is selected, the value here is 0-0-1 */
    value?: string | Set<string>;
    inputValue: string;
    placeholder?: string;
    /** The function used to update the value of the input box. You
     * should call this function when the value of the Input component
     * customized by triggerRender is updated to synchronize the state
     * with Cascader. */
    onSearch: (inputValue: string) => void;
    onChange: (inputValue: string) => void;
    onClear: (e: any) => void;
    onRemove: (key: string) => void;
}
export interface BasicScrollPanelProps {
    panelIndex: number;
    activeNode: BasicCascaderData;
}
export interface BasicCascaderProps {
    borderless?: boolean;
    mouseEnterDelay?: number;
    mouseLeaveDelay?: number;
    separator?: string;
    arrowIcon?: any;
    changeOnSelect?: boolean;
    multiple?: boolean;
    autoMergeValue?: boolean;
    defaultValue?: BasicValue;
    disabled?: boolean;
    dropdownClassName?: string;
    dropdownStyle?: any;
    emptyContent?: any;
    filterLeafOnly?: boolean;
    motion?: boolean;
    filterTreeNode?: ((inputValue: string, treeNodeString: string, data?: BasicCascaderData) => boolean) | boolean;
    filterSorter?: (first: BasicCascaderData, second: BasicCascaderData, inputValue: string) => number;
    filterRender?: (props: any) => any;
    placeholder?: string;
    searchPlaceholder?: string;
    size?: CascaderType;
    className?: string;
    treeData?: Array<BasicCascaderData>;
    treeNodeFilterProp?: string;
    displayProp?: string;
    maxTagCount?: number;
    max?: number;
    showRestTagsPopover?: boolean;
    restTagsPopoverProps?: any;
    children?: any;
    zIndex?: number;
    value?: BasicValue;
    prefix?: any;
    suffix?: any;
    insetLabel?: any;
    style?: any;
    stopPropagation?: boolean | string;
    showClear?: boolean;
    autoAdjustOverflow?: boolean;
    defaultOpen?: boolean;
    onChangeWithObject?: boolean;
    bottomSlot?: any;
    topSlot?: any;
    showNext?: ShowNextType;
    disableStrictly?: boolean;
    leafOnly?: boolean;
    enableLeafClick?: boolean;
    preventScroll?: boolean;
    virtualizeInSearch?: Virtualize;
    checkRelation?: string;
    onClear?: () => void;
    triggerRender?: (props: BasicTriggerRenderProps) => any;
    onListScroll?: (e: any, panel: BasicScrollPanelProps) => void;
    loadData?: (selectOptions: BasicCascaderData[]) => Promise<void>;
    onLoad?: (newLoadedKeys: Set<string>, data: BasicCascaderData) => void;
    onDropdownVisibleChange?: (visible: boolean) => void;
    getPopupContainer?: () => HTMLElement;
    onChange?: (value: BasicValue) => void;
    onSearch?: (value: string) => void;
    onSelect?: (value: string | number | Array<string | number>) => void;
    onExceed?: (checkedItem: BasicEntity[]) => void;
    displayRender?: (selected: Array<string> | BasicEntity, idx?: number) => any;
    onBlur?: (e: any) => void;
    onFocus?: (e: any) => void;
}
export interface BasicCascaderInnerData {
    emptyContentMinWidth: number;
    isOpen: boolean;
    rePosKey: number;
    keyEntities: BasicEntities;
    selectedKeys: Set<string>;
    activeKeys: Set<string>;
    filteredKeys: Set<string>;
    inputValue: string;
    isSearching: boolean;
    inputPlaceHolder: string;
    prevProps: BasicCascaderProps;
    isHovering: boolean;
    checkedKeys: Set<string>;
    halfCheckedKeys: Set<string>;
    resolvedCheckedKeys: Set<string>;
    loadedKeys: Set<string>;
    loadingKeys: Set<string>;
    loading: boolean;
    treeData?: Array<BasicCascaderData>;
    isFocus?: boolean;
    isInput?: boolean;
    disabledKeys?: Set<string>;
    showInput?: boolean;
}
export interface CascaderAdapter extends DefaultAdapter<BasicCascaderProps, BasicCascaderInnerData> {
    notifyClear?: () => void;
    updateInputValue: (value: string) => void;
    updateInputPlaceHolder: (value: string) => void;
    focusInput: () => void;
    blurInput: () => void;
    registerClickOutsideHandler: (cb: (e: any) => void) => void;
    unregisterClickOutsideHandler: () => void;
    rePositionDropdown: () => void;
    updateStates: (states: Partial<BasicCascaderInnerData>) => void;
    openMenu: () => void;
    closeMenu: (cb?: () => void) => void;
    updateSelection: (selectedKeys: Set<string>) => void;
    notifyChange: (value: BasicValue) => void;
    notifySelect: (selected: string | number | Array<string | number>) => void;
    notifyOnSearch: (input: string) => void;
    notifyFocus: (e: any) => void;
    notifyBlur: (e: any) => void;
    notifyDropdownVisibleChange: (visible: boolean) => void;
    toggleHovering: (bool: boolean) => void;
    notifyLoadData: (selectedOpt: BasicCascaderData[], callback: (data?: BasicEntities) => void) => void;
    notifyOnLoad: (newLoadedKeys: Set<string>, data: BasicCascaderData) => void;
    notifyListScroll: (e: any, panel: BasicScrollPanelProps) => void;
    notifyOnExceed: (data: BasicEntity[]) => void;
    toggleInputShow: (show: boolean, cb: () => void) => void;
    updateFocusState: (focus: boolean) => void;
    updateLoadingKeyRefValue: (keys: Set<string>) => void;
    getLoadingKeyRefValue: () => Set<string>;
    updateLoadedKeyRefValue: (keys: Set<string>) => void;
    getLoadedKeyRefValue: () => Set<string>;
    setEmptyContentMinWidth: (minWidth: number) => void;
    getTriggerWidth: () => number;
}
export default class CascaderFoundation extends BaseFoundation<CascaderAdapter, BasicCascaderProps, BasicCascaderInnerData> {
    constructor(adapter: CascaderAdapter);
    init(): void;
    _setEmptyContentMinWidth(): void;
    handleKeyDown: (e: any) => void;
    destroy(): void;
    _isDisabled(): any;
    _isFilterable(): boolean;
    _notifyChange(item: BasicEntity | BasicData | Set<string>): void;
    _isLeaf(item: BasicCascaderData): boolean;
    _clearInput(): void;
    _notifyBlur(e: any): void;
    _notifyFocus(e: any): void;
    _isOptionDisabled(key: string, keyEntities: BasicEntities): boolean;
    getItemPropPath(selectedKey: string, prop: string | any[], keyEntities?: BasicEntities): any[];
    _getCacheValue(keyEntities: BasicEntities): any;
    collectOptions(init?: boolean): void;
    handleValueChange(value: BasicValue): void;
    /**
     * When single selection, the clear objects of
     * selectedKeys, activeKeys, filteredKeys, input, etc.
     */
    _getClearSelectedKey(filterable: boolean): Partial<BasicCascaderInnerData>;
    updateSelectedKey(value: BasicValue, keyEntities: BasicEntities): void;
    open(): void;
    reCalcActiveKeys(): void;
    close(e: any, key?: string): void;
    focus(): void;
    blur(): void;
    toggle2SearchInput(isShow: boolean): void;
    updateSearching: (isSearching: boolean) => void;
    handleItemClick(e: any, item: BasicEntity | BasicData): void;
    handleItemHover(e: any, item: BasicEntity): void;
    handleShowNextByHover(item: BasicEntity): void;
    onItemCheckboxClick(item: BasicEntity | BasicData): void;
    handleClick(e: any): void;
    /**
     * A11y: simulate selection click
     */
    handleSelectionEnterPress(keyboardEvent: any): void;
    toggleHoverState(bool: boolean): void;
    _defaultRenderText(path: any[], displayRender?: BasicCascaderProps['displayRender']): any;
    renderDisplayText(targetKey: string, keyEntities?: BasicEntities): any;
    handleNodeLoad(item: BasicEntity | BasicData): void;
    notifyIfLoadData(item: BasicEntity | BasicData): void;
    handleSingleSelect(e: any, item: BasicEntity | BasicData): void;
    _handleMultipleSelect(item: BasicEntity | BasicData): void;
    _handleRelatedMultipleSelect(item: BasicEntity | BasicData): void;
    _handleUnRelatedMultipleSelect(item: BasicEntity | BasicData): void;
    calcNonDisabledCheckedKeys(eventKey: string, targetStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<any>;
    };
    calcCheckedStatus(targetStatus: boolean, eventKey: string): boolean;
    _notifySelect(keys: Set<string>): void;
    /**
     * calculate all key of nodes that are checked or half checked
     * @param {string} key key of node
     * @param {boolean} curCheckedStatus checked status of node
     */
    calcCheckedKeys(key: string, curCheckedStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<string>;
    };
    handleInputChange(sugInput: string): void;
    handleClear(): void;
    /**
     * A11y: simulate clear button click
     */
    handleClearEnterPress(keyboardEvent: any): void;
    getRenderData(): BasicEntity[] | BasicData[];
    getFilteredData(): BasicData[];
    handleListScroll(e: any, ind: number): void;
    handleTagRemoveByKey: (key: string) => void;
    handleTagRemoveInTrigger: (pos: string) => void;
}
