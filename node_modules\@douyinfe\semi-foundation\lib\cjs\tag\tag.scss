//@import '../theme/variables.scss';
@import "./variables.scss";
@import "./mixin.scss";
$module: #{$prefix}-tag;

$colors: "amber", "blue", "cyan", "green", "grey", "indigo", "light-blue", "light-green", "lime", "orange", "pink",
    "purple", "red", "teal", "violet", "yellow";

$types: "ghost", "solid", "light";

.#{$module} {
    box-sizing: border-box;
    border-radius: $radius-tag;
    background-color: transparent;
    position: relative;
    user-select: none;
    // hidden avatar tag image
    overflow: hidden;
    white-space: nowrap;
    vertical-align: bottom;
    @include all-center;
    display: inline-flex;

    &-default,
    &-small {
        @include font-size-small;
        height: $height-tag_small;
        padding: $spacing-tag_small-paddingY $spacing-tag_small-paddingX;
        &:focus-visible {
            outline: $width-tag-outline solid $color-tag-outline-focus;
        }
    }

    &-square {
        border-radius: $radius-tag;
    }
    &-circle {
        border-radius: $radius-tag_circle;
    }

    &-large {
        @include font-size-small;
        padding: $spacing-tag_large-paddingY $spacing-tag_large-paddingX;
        height: $height-tag_large;
        &:focus-visible {
            outline: $width-tag-outline solid $color-tag-outline-focus;
        }
    }

    &-invisible {
        display: none;
    }

    &-prefix-icon {
        display: flex;
        padding-right: $spacing-tag_prefix_icon_paddingRight;
    }

    &-suffix-icon {
        display: flex;
        padding-left: $spacing-tag_suffix_icon_paddingLeft;
    }

    &-content {
        flex: 1;

        &-ellipsis {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        &-center {
            display: flex;
            @include all-center;
            height: 100%;
            min-width: 0;
        }
    }

    &-close {
        @include all-center;
        color: $color-tag_close-icon-default;
        padding-left: $spacing-tag_close-paddingLeft;
        cursor: pointer;

        &:hover {
            color: $color-tag_close-icon-hover;
        }

        &:active {
            color: $color-tag_close-icon-active;
        }
    }

    &-closable {
        padding: $spacing-tag_closable-paddingTop $spacing-tag_closable-paddingRight $spacing-tag_closable-paddingBottom
            $spacing-tag_closable-paddingLeft;
    }

    &-avatar-square,
    &-avatar-circle {
        .#{$prefix}-avatar {
            margin-right: $spacing-tag_avatar-marginRight;
        }
    }

    &-avatar-square {
        padding: $spacing-tag_avatar_square-paddingTop $spacing-tag_avatar_square-paddingRight
            $spacing-tag_avatar_square-paddingBottom $spacing-tag_avatar_square-paddingLeft;

        .#{$prefix}-avatar {
            & > img {
                background-color: $color-tag_avatar_square_img-bg-default;
            }
        }
    }

    &-avatar-circle {
        padding: $spacing-tag_avatar_circle-paddingTop $spacing-tag_avatar_circle-paddingRight
            $spacing-tag_avatar_circle-paddingBottom $spacing-tag_avatar_circle-paddingLeft;
    }

    &-avatar-square.#{$module}-default,
    &-avatar-square.#{$module}-small {
        .#{$prefix}-avatar {
            width: $height-tag_small;
            height: $height-tag_small;
        }
    }

    &-avatar-square.#{$module}-large {
        .#{$prefix}-avatar {
            width: $height-tag_large;
            height: $height-tag_large;
        }
    }

    &-avatar-circle.#{$module}-small,
    &-avatar-circle.#{$module}-default {
        // when avatarShape=circle change tag border radius
        border-radius: $height-tag_small * 0.5 + 1;

        .#{$prefix}-avatar {
            width: $width-tag_avatar_circle_small;
            height: $width-tag_avatar_circle_small;
        }
    }

    &-avatar-circle.#{$module}-large {
        border-radius: $height-tag_large * 0.5 + 1;

        .#{$prefix}-avatar {
            width: $width-tag_avatar_circle_large;
            height: $width-tag_avatar_circle_large;
        }
    }
}

.#{$module}-group {
    display: block;
    height: auto;

    .#{$module} {
        margin-bottom: $spacing-tag_group-marginBottom;
        margin-right: $spacing-tag_group-marginRight;
    }

    &-max.#{$module}-group-small {
        height: ($height-tag_small + 2);
    }
    &-max.#{$module}-group-large {
        height: ($height-tag_large + 2);
    }
    // &-small {
    //     height: ($height-tag_small + 2);
    // }
    // &-large {
    //     height: ($height-tag_large + 2);
    // }
}

.#{$module}-rest-group-popover {
    .#{$module} {
        margin-right: $spacing-tag_group-marginRight;
        margin-bottom: $spacing-tag_group-marginBottom;
        &:last-of-type {
            margin-right: 0;
        }
    }
}

@each $c in $colors {
    @each $t in $types {
        .#{$module}-#{$c}-#{$t} {
            @include tag-style($c, $t);
        }
    }
}

@each $t in $types {
    .#{$module}-white-#{$t} {
        background-color: $color-tag_white-bg-default;
        border: $width-tag-border solid $color-tag_white-border-default;
        color: $color-tag_white-text-default;
        // .#{$module}-close {
        //     color: $tag-color-white-default-icon;
        // }
    }
}

.#{$module}-white-ghost,
.#{$module}-white-light,
.#{$module}-white-solid {
    .#{$module}-close {
        color: $color-tag_white-icon-default;
    }
}

.#{$module}-avatar-square,
.#{$module}-avatar-circle {
    background-color: $color-tag_avatar-bg-default;
    border: $width-tag_avatar-border solid $color-tag_avatar-border-default;
    color: $color-tag_avatar-text-default;
}

.#{$module}-solid {
    .#{$module}-close {
        color: $color-tag_close-icon_deep-default;
        opacity: .8;

        &:hover {
            opacity: 1.0;
        }

        &:active {
            opacity: .9;
        }
    }
}

@import './rtl.scss';
