"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _isUndefined2 = _interopRequireDefault(require("lodash/isUndefined"));
var _isEqual2 = _interopRequireDefault(require("lodash/isEqual"));
var _slice2 = _interopRequireDefault(require("lodash/slice"));
var _isMap2 = _interopRequireDefault(require("lodash/isMap"));
var _filter2 = _interopRequireDefault(require("lodash/filter"));
var _isSet2 = _interopRequireDefault(require("lodash/isSet"));
var _pull2 = _interopRequireDefault(require("lodash/pull"));
var _some2 = _interopRequireDefault(require("lodash/some"));
var _find2 = _interopRequireDefault(require("lodash/find"));
var _each2 = _interopRequireDefault(require("lodash/each"));
var _isFunction2 = _interopRequireDefault(require("lodash/isFunction"));
var _merge2 = _interopRequireDefault(require("lodash/merge"));
var _get2 = _interopRequireDefault(require("lodash/get"));
var _memoizeOne = _interopRequireDefault(require("memoize-one"));
var _foundation = _interopRequireDefault(require("../base/foundation"));
var _constants = require("./constants");
var _utils = require("./utils");
var _array = require("../utils/array");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
class TableFoundation extends _foundation.default {
  /**
   * update columns in place, and use default values as initial values if the sorting and filtering columns have no values
   */
  static initColumnsFilteredValueAndSorterOrder(columns) {
    columns.forEach(column => {
      TableFoundation.initFilteredValue(column);
      TableFoundation.initSorterOrder(column);
    });
    return columns;
  }
  /**
   * init filteredValue of filtering column, use defaultFilteredValue or [] when it is undefined
   */
  static initFilteredValue(column) {
    const {
      defaultFilteredValue,
      filteredValue
    } = column;
    // There may be cases where onFilter is empty, such as server-side filtering
    // Because filterValue affects the output of filters, it needs to be initialized here
    if ((0, _isUndefined2.default)(filteredValue)) {
      if (Array.isArray(defaultFilteredValue) && defaultFilteredValue.length) {
        column.filteredValue = defaultFilteredValue;
      } else {
        column.filteredValue = [];
      }
    }
  }
  /**
   * init sortOrder of sorting column, use defaultSortOrder or [] when it is undefined
   */
  static initSorterOrder(column) {
    const {
      defaultSortOrder,
      sortOrder,
      sorter
    } = column;
    if (sorter && (0, _isUndefined2.default)(sortOrder)) {
      if (!(0, _isUndefined2.default)(defaultSortOrder)) {
        column.sortOrder = defaultSortOrder;
      } else {
        column.sortOrder = false;
      }
    }
  }
  constructor(adapter) {
    super(Object.assign({}, adapter));
    /**
     * set page number
     */
    this.setPage = (currentPage, currentPageSize) => {
      currentPage = currentPage || this._adapter.getCurrentPage();
      const currentPagination = this.getState('pagination');
      const {
        dataSource,
        pagination,
        disabledRowKeys,
        allRowKeys
      } = this.getCurrentPageData(null, Object.assign(Object.assign({}, currentPagination), {
        currentPage,
        pageSize: currentPageSize
      }));
      if (!this._pagerIsControlled() && currentPage > 0) {
        this._adapter.setDisabledRowKeys(disabledRowKeys);
        this._adapter.setAllRowKeys(allRowKeys);
        this._adapter.setPagination(pagination);
        this._adapter.setDataSource(dataSource);
      }
      this._notifyChange(pagination, undefined, undefined, {
        changeType: 'pagination'
      });
    };
    /**
     * Cache related data when initializing or updating the calculated dataSource
     * @param {*} filteredSortedDataSource
     */
    this.setCachedFilteredSortedDataSource = filteredSortedDataSource => {
      this._adapter.setCachedFilteredSortedDataSource(filteredSortedDataSource);
      const filteredSortedRowKeys = this.getAllRowKeys(filteredSortedDataSource);
      this._adapter.setCachedFilteredSortedRowKeys(filteredSortedRowKeys);
    };
    this.handleMouseDown = e => {
      var _a, _b;
      this.mouseDownTarget = {
        targetName: (_a = e.target) === null || _a === void 0 ? void 0 : _a.tagName,
        className: (_b = e.target) === null || _b === void 0 ? void 0 : _b.className
      };
    };
    this.isSortOrderValid = sortOrder => _constants.strings.SORT_DIRECTIONS.includes(sortOrder) || sortOrder === false;
    /**
     * memoized function list
     */
    const handleColumns = this._adapter.getHandleColumns();
    const mergePagination = this._adapter.getMergePagination();
    this.mouseDownTarget = null;
    this.memoizedWithFnsColumns = (0, _memoizeOne.default)(handleColumns, _isEqual2.default);
    this.memoizedFilterColumns = (0, _memoizeOne.default)(_utils.filterColumns);
    this.memoizedFlattenFnsColumns = (0, _memoizeOne.default)(_utils.flattenColumns);
    this.memoizedPagination = (0, _memoizeOne.default)(mergePagination, _isEqual2.default);
  }
  init() {
    const dataSource = [...this.getProp('dataSource')];
    const {
      queries
    } = this._adapter.getStates();
    const filteredSortedDataSource = this.getFilteredSortedDataSource(dataSource, queries);
    const allDataDisabledRowKeys = this.getAllDisabledRowKeys(filteredSortedDataSource);
    const pageData = this.getCurrentPageData(filteredSortedDataSource);
    this.setAdapterPageData(pageData);
    this.initExpandedRowKeys(pageData);
    this.initSelectedRowKeys(pageData);
    // cache dataSource after mount, and then calculate it on demand
    this.setCachedFilteredSortedDataSource(filteredSortedDataSource);
    this.setAllDisabledRowKeys(allDataDisabledRowKeys);
  }
  initExpandedRowKeys() {
    let {
      groups
    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    const {
      defaultExpandAllRows,
      defaultExpandedRowKeys = [],
      expandedRowKeys: propExpandedRowKeys = [],
      dataSource = [],
      expandAllRows,
      defaultExpandAllGroupRows,
      expandAllGroupRows
    } = this.getProps();
    const expandedRowKeys = [];
    if (defaultExpandAllRows || expandAllRows) {
      this._addNoDuplicatedItemsToArr(expandedRowKeys, this.getAllRowKeys(dataSource), groups && (0, _isMap2.default)(groups) && groups.size ? Array.from(groups.keys()) : []);
    } else if (defaultExpandAllGroupRows || expandAllGroupRows) {
      this._addNoDuplicatedItemsToArr(expandedRowKeys, propExpandedRowKeys, groups && (0, _isMap2.default)(groups) && groups.size ? Array.from(groups.keys()) : []);
    } else if (Array.isArray(defaultExpandedRowKeys) && defaultExpandedRowKeys.length) {
      this._addNoDuplicatedItemsToArr(expandedRowKeys, defaultExpandedRowKeys);
    } else if (Array.isArray(propExpandedRowKeys) && propExpandedRowKeys.length) {
      this._addNoDuplicatedItemsToArr(expandedRowKeys, propExpandedRowKeys);
    }
    this._adapter.setExpandedRowKeys(expandedRowKeys);
  }
  initSelectedRowKeys(_ref) {
    let {
      disabledRowKeys
    } = _ref;
    const rowSelection = this.getProp('rowSelection');
    const rowKeys = [];
    if (rowSelection) {
      const selectedRowKeys = (0, _get2.default)(rowSelection, 'selectedRowKeys');
      const defaultSelectedRowKeys = (0, _get2.default)(rowSelection, 'defaultSelectedRowKeys');
      if (Array.isArray(selectedRowKeys)) {
        this._addNoDuplicatedItemsToArr(rowKeys, selectedRowKeys);
      } else if (Array.isArray(defaultSelectedRowKeys)) {
        this._addNoDuplicatedItemsToArr(rowKeys, defaultSelectedRowKeys);
      }
      if (Array.isArray(disabledRowKeys) && disabledRowKeys.length) {
        (0, _pull2.default)(rowKeys, ...disabledRowKeys);
      }
      this._adapter.setSelectedRowKeys(rowKeys);
    }
  }
  /**
   * Get filtered and sorted data
   * @param {Object[]} dataSource
   * @param {Object[]} queries
   * @returns {Object[]} sortedDataSource
   */
  getFilteredSortedDataSource(dataSource, queries) {
    const filteredDataSource = this.filterDataSource(dataSource, queries.filter(query => {
      const currentFilteredValue = query.filteredValue ? query.filteredValue : query.defaultFilteredValue;
      return (0, _isFunction2.default)(query.onFilter) && Array.isArray(currentFilteredValue) && currentFilteredValue.length;
    }));
    const sortedDataSource = this.sortDataSource(filteredDataSource, queries.filter(query => query && (0, _isFunction2.default)(query.sorter)));
    return sortedDataSource;
  }
  /**
   * get current page data
   *
   * @param {Array} dataSource
   * @param {object} pagination
   * @param {object} queries
   * @returns {{dataSource: RecordType[], groups: Map<string, Set<string>>, pagination: object, disabledRowKeys: string[], queries: BaseColumnProps[], allRowKeys: string[]}}
   */
  getCurrentPageData(dataSource, pagination, queries) {
    const filteredSortedDataSource = this._adapter.getCachedFilteredSortedDataSource();
    dataSource = dataSource == null ? [...filteredSortedDataSource] : dataSource;
    pagination = pagination == null ? this.getState('pagination') && Object.assign({}, this.getState('pagination')) : pagination;
    queries = queries == null ? [...this.getState('queries')] : queries;
    let groups;
    if (this.getProp('groupBy') != null) {
      const {
        groups: groupedGroups,
        dataSource: groupedData
      } = this.groupDataSource(dataSource);
      dataSource = groupedData;
      groups = groupedGroups;
    }
    pagination = this.normalizePagination(pagination, dataSource);
    dataSource = this.limitPageDataSource(dataSource, pagination);
    const disabledRowKeys = this.getAllDisabledRowKeys(dataSource);
    const allRowKeys = this.getAllRowKeys(dataSource);
    const pageData = {
      dataSource,
      groups,
      pagination,
      disabledRowKeys,
      allRowKeys,
      queries
    };
    return pageData;
  }
  /**
   * group dataSource, return grouped row keys
   *
   * @param {*[]} dataSource
   * @param {Function|string} groupBy
   */
  groupDataSource(dataSource, groupBy) {
    groupBy = groupBy == null ? this.getProp('groupBy') : groupBy;
    const groups = new Map();
    const newDataSource = [];
    if (groupBy != null) {
      (0, _each2.default)(dataSource, (record, index) => {
        const groupKey = typeof groupBy === 'function' ? groupBy(record) : (0, _get2.default)(record, groupBy);
        if (groupKey != null && groupKey !== '') {
          const recordKey = this.getRecordKey(record);
          let group = groups.get(groupKey);
          if (!(0, _isSet2.default)(group)) {
            group = new Set([recordKey]);
            groups.set(groupKey, group);
          } else {
            group.add(recordKey);
          }
        }
      });
    }
    if (groups && groups.size) {
      groups.forEach((set, key) => {
        if ((0, _isSet2.default)(set)) {
          set.forEach(realKey => {
            newDataSource.push(this._getRecord(realKey));
          });
        }
      });
    } else {
      newDataSource.push(...dataSource);
    }
    return {
      groups,
      dataSource: newDataSource
    };
  }
  /**
   * sort data
   *
   * @param {Array} dataSource
   * @param {Array} sorters
   * @returns {Array}
   */
  sortDataSource(dataSource, sorters) {
    (0, _each2.default)(sorters, sorterObj => {
      // const sorterObj = last(sorters) || {};
      const {
        sorter,
        sortOrder,
        defaultSortOrder,
        sortChildrenRecord
      } = sorterObj;
      const currentSortOrder = this.isSortOrderValid(sortOrder) ? sortOrder : defaultSortOrder;
      if ((0, _isFunction2.default)(sorter) && currentSortOrder && _constants.strings.SORT_DIRECTIONS.includes(currentSortOrder)) {
        if (sortChildrenRecord) {
          const childrenRecordName = this.getProp('childrenRecordName');
          dataSource = dataSource && dataSource.map(record => {
            const children = this._getRecordChildren(record);
            if (Array.isArray(children) && children.length) {
              return Object.assign(Object.assign({}, record), {
                [childrenRecordName]: this.sortDataSource(children, [sorterObj])
              });
            }
            return record;
          });
        }
        dataSource.sort((0, _array.withOrderSort)(sorter, currentSortOrder));
        return false;
      }
      return undefined;
    });
    return dataSource;
  }
  /**
   * filter data source
   *
   * @param {*[]} dataSource
   * @param {*[]} filters
   * @returns {*[]}
   */
  filterDataSource(dataSource, filters) {
    let filteredData = null;
    let hasValidFilters = false;
    const childrenRecordName = this.getProp('childrenRecordName');
    (0, _each2.default)(filters, filterObj => {
      const {
        onFilter,
        filteredValue,
        filterChildrenRecord,
        defaultFilteredValue
      } = filterObj;
      const currentFilteredValue = Array.isArray(filteredValue) ? filteredValue : defaultFilteredValue;
      if (typeof onFilter === 'function' && Array.isArray(currentFilteredValue) && currentFilteredValue.length) {
        hasValidFilters = true;
        if (filteredData === null) {
          filteredData = new Map();
        } else {
          dataSource = Array.from(filteredData && filteredData.values());
          filteredData = new Map();
        }
        (0, _each2.default)(dataSource, record => {
          (0, _each2.default)(currentFilteredValue, value => {
            const childrenRecords = (0, _get2.default)(record, childrenRecordName);
            const recordKey = this.getRecordKey(record);
            let filteredChildren;
            if (Array.isArray(childrenRecords) && childrenRecords.length && filterChildrenRecord) {
              filteredChildren = this.filterDataSource(childrenRecords, [filterObj]);
            }
            if (Array.isArray(filteredChildren) && filteredChildren.length) {
              if (recordKey != null) {
                const children = (0, _get2.default)(filteredData.get(recordKey), childrenRecordName, []);
                filteredData.set(recordKey, Object.assign(Object.assign({}, record), {
                  [childrenRecordName]: filteredChildren.reduce((arr, cur) => {
                    if (arr.find(item => this.getRecordKey(item) === this.getRecordKey(cur)) == null) {
                      arr.push(cur);
                    }
                    return arr;
                  },
                  // @ts-ignore
                  [...children])
                }));
              }
            } else if (onFilter(value, record)) {
              filteredData.set(recordKey, record);
            }
          });
        });
      }
    });
    if (hasValidFilters) {
      dataSource = Array.from(filteredData && filteredData.values());
    }
    return dataSource;
  }
  limitPageDataSource(dataSource, pagination) {
    dataSource = dataSource == null ? this.getProp('dataSource') : dataSource;
    pagination = pagination == null ? this.getState('pagination') : pagination;
    let pageData = dataSource;
    const pageNo = (0, _get2.default)(pagination, 'currentPage');
    if (this.getProp('pagination') !== false && pageNo && dataSource && pagination && !this._pagerIsControlled()) {
      const {
        pageSize = _constants.numbers.DEFAULT_PAGE_SIZE
      } = pagination;
      const start = (pageNo - 1) * pageSize;
      const end = pageNo * pageSize;
      pageData = (0, _slice2.default)(dataSource, start, end);
    }
    return pageData;
  }
  normalizePagination(pagination, dataSource) {
    pagination = pagination == null ? this._getPagination() : pagination;
    dataSource = dataSource == null ? this._getDataSource() : dataSource;
    const propPagination = this.getProp('pagination');
    if (pagination) {
      pagination = typeof pagination === 'object' ? Object.assign({}, pagination) : {};
      pagination = (0, _merge2.default)({
        total: dataSource && dataSource.length || 0,
        pageSize: _constants.numbers.DEFAULT_PAGE_SIZE,
        currentPage: (0, _get2.default)(propPagination, 'defaultCurrentPage', 1),
        position: _constants.strings.PAGINATION_POSITIONS[0]
      }, pagination);
      if (!this._pagerIsControlled()) {
        const total = (0, _get2.default)(propPagination, 'total', dataSource.length);
        const {
          currentPage,
          pageSize
        } = pagination;
        const realTotalPage = Math.ceil(total / pageSize);
        pagination.total = total;
        if (currentPage > realTotalPage) {
          pagination.currentPage = 1;
        }
      }
    }
    return pagination;
  }
  setAdapterPageData() {
    let pageData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    const {
      pagination,
      dataSource,
      disabledRowKeys,
      allRowKeys,
      groups
    } = pageData;
    this._adapter.setDisabledRowKeys(disabledRowKeys);
    this._adapter.setAllRowKeys(allRowKeys);
    this._adapter.setPagination(pagination);
    this._adapter.setGroups(groups);
    this._adapter.setDataSource(dataSource);
  }
  destroy() {}
  setAllDisabledRowKeys(disabledRowKeys) {
    this._adapter.setAllDisabledRowKeys(disabledRowKeys);
  }
  handleClick(e) {}
  handleMouseEnter(e) {}
  handleMouseLeave(e) {}
  stopPropagation(e) {
    this._adapter.stopPropagation(e);
  }
  /**
   * Add non-repeating elements to the array itself
   */
  _addNoDuplicatedItemsToArr() {
    let srcArr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
    for (var _len = arguments.length, objArrs = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      objArrs[_key - 1] = arguments[_key];
    }
    for (const objArr of objArrs) {
      if (Array.isArray(objArr)) {
        for (const item of objArr) {
          if (!srcArr.includes(item)) {
            srcArr.push(item);
          }
        }
      }
    }
    return srcArr;
  }
  _notifyChange(pagination, filters, sorter, extra) {
    pagination = pagination == null ? this._getPagination() : pagination;
    filters = filters == null ? this._getAllFilters() : filters;
    sorter = sorter == null ? this._getAllSorters()[0] : sorter;
    if ((0, _get2.default)(this.getProp('scroll'), 'scrollToFirstRowOnChange')) {
      this._adapter.resetScrollY();
    }
    this._adapter.notifyChange({
      pagination: Object.assign({}, pagination),
      filters: [...filters],
      sorter,
      extra: Object.assign({}, extra)
    });
  }
  _rowExpansionIsControlled() {
    return Array.isArray(this.getProp('expandedRowKeys'));
  }
  _pagerIsControlled() {
    return (0, _get2.default)(this.getProp('pagination'), 'currentPage') != null;
  }
  _selectionIsControlled() {
    return Array.isArray((0, _get2.default)(this.getProp('rowSelection'), 'selectedRowKeys'));
  }
  /**
   * Determine whether the column sorting is controlled
   * Controlled: the column passed the sortOrder prop
   * @param {String} dataIndex
   * @returns {Boolean}
   */
  _sorterIsControlled(dataIndex) {
    // The basis for judgment should be props columns instead of cachedColumns fix#1141
    const query = dataIndex && this.getQuery(dataIndex, this.getState('flattenColumns'));
    return Boolean(query && query.sortOrder != null);
  }
  /**
   * Determine whether the column is filtered and controlled
   * Controlled: the column passed the filteredValue prop
   * @param {String} dataIndex
   * @returns {Boolean}
   */
  _filterIsControlled(dataIndex) {
    const query = dataIndex && this.getQuery(dataIndex, this.getState('flattenColumns'));
    return Boolean(query && Array.isArray(query.filteredValue));
  }
  _filterShowIsControlled(dataIndex) {
    const query = dataIndex && this.getQuery(dataIndex, this.getState('flattenColumns'));
    return Boolean(query && (query.filterDropdownVisible === true || query.filterDropdownVisible === false));
  }
  _getSelectedRowKeys() {
    const rowSelection = this.getState('rowSelection');
    const selectedRowKeys = (0, _get2.default)(rowSelection, 'selectedRowKeys', []);
    return [...selectedRowKeys];
  }
  _getSelectedRowKeysSet() {
    const rowSelection = this.getState('rowSelection');
    const selectedRowKeysSet = (0, _get2.default)(rowSelection, 'selectedRowKeysSet', new Set());
    return selectedRowKeysSet;
  }
  _getDataSource() {
    return this.getProp('dataSource') || [];
  }
  _getRecord(realKey) {
    return (0, _find2.default)(this.getProp('dataSource'), record => realKey != null && realKey !== '' && this.getRecordKey(record) === realKey);
  }
  _getRecordChildren(record) {
    return (0, _get2.default)(record, this.getProp('childrenRecordName'));
  }
  _getPagination() {
    return this.getState('pagination') || {};
  }
  /**
   * Filters are considered valid if filteredValue exists
   */
  _getAllFilters(queries) {
    queries = queries || this.getState('queries');
    const filters = [];
    (0, _each2.default)(queries, query => {
      if (Array.isArray(query.filteredValue) && (query.filteredValue.length || this._filterIsControlled(query.dataIndex))) {
        filters.push(query);
      }
    });
    return filters;
  }
  _getAllSorters(queries) {
    queries = queries || this.getState('queries');
    return (0, _filter2.default)(queries, query => query.sorter && query.sortOrder);
  }
  _filterQueries(targetQuery, queries) {
    let keys = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ['dataIndex'];
    queries = queries == null ? this.getState('queries') : queries;
    const filteredQueries = [];
    const filteredIndexes = [];
    (0, _each2.default)(queries, (itQuery, index) => {
      const flag = (0, _some2.default)(keys, k => k && targetQuery[k] != null && targetQuery[k] === itQuery[k]);
      if (flag) {
        filteredQueries.push(itQuery);
        filteredIndexes.push(index);
      }
    });
    return {
      filteredQueries,
      filteredIndexes
    };
  }
  _mergeToQueries(query, queries) {
    let keys = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ['dataIndex'];
    queries = queries == null ? this.getState('queries') : queries;
    queries = [...queries];
    query = Object.assign({}, query);
    const {
      filteredQueries,
      filteredIndexes
    } = this._filterQueries(query, queries, keys);
    (0, _each2.default)(filteredQueries, (curQuery, idx) => {
      // assign(curQuery, query);
      queries[filteredIndexes[idx]] = Object.assign({}, query);
    });
    return queries;
  }
  /**
   * get record real key
   * @param {RecordType} record
   * @returns {string}
   */
  getRecordKey(record) {
    if (!record) {
      return undefined;
    }
    const rowKey = this.getProp('rowKey');
    return typeof rowKey === 'function' ? rowKey(record) : (0, _get2.default)(record, rowKey);
  }
  isEmpty(dataSource) {
    dataSource = dataSource == null ? this.getProp('dataSource') : dataSource;
    return !(Array.isArray(dataSource) && dataSource.length > 0);
  }
  handleSelectRow(realKey, selected, e) {
    this.stopPropagation(e);
    if (typeof selected === 'boolean' && realKey != null) {
      const selectedRowKeys = this._getSelectedRowKeys();
      let foundIdx = -1;
      const selectedRow = this.getSelectedRows(null, [realKey])[0];
      let selectedRows;
      if ((foundIdx = selectedRowKeys.indexOf(realKey)) > -1 && selected === false) {
        selectedRowKeys.splice(foundIdx, 1);
        selectedRows = this.getSelectedRows(null, selectedRowKeys);
        if (!this._selectionIsControlled()) {
          this._adapter.setSelectedRowKeys(selectedRowKeys);
        }
        this._adapter.notifySelect(selectedRow, selected, selectedRows, e);
        this._adapter.notifySelectionChange(selectedRowKeys, selectedRows);
      } else if (selectedRowKeys.indexOf(realKey) === -1 && selected === true) {
        selectedRowKeys.push(realKey);
        selectedRows = this.getSelectedRows(null, selectedRowKeys);
        if (!this._selectionIsControlled()) {
          this._adapter.setSelectedRowKeys(selectedRowKeys);
        }
        this._adapter.notifySelect(selectedRow, selected, selectedRows, e);
        this._adapter.notifySelectionChange(selectedRowKeys, selectedRows);
      }
    }
  }
  /**
   * select all rows
   * @param {*} selected The future state of the select all button
   * @param {*} e
   */
  handleSelectAllRow(selected, e) {
    this.stopPropagation(e);
    if (typeof selected === 'boolean') {
      const curSelectedRowKeys = this._getSelectedRowKeys();
      let selectedRowKeys = [...curSelectedRowKeys];
      const selectedRowKeysSet = this._getSelectedRowKeysSet();
      let allRowKeys = [...this._adapter.getCachedFilteredSortedRowKeys()];
      const disabledRowKeys = this._adapter.getAllDisabledRowKeys();
      const disabledRowKeysSet = this._adapter.getAllDisabledRowKeysSet();
      let changedRowKeys;
      // Select all, if not disabled && not in selectedRowKeys
      if (selected) {
        for (const key of allRowKeys) {
          if (!disabledRowKeysSet.has(key) && !selectedRowKeysSet.has(key)) {
            selectedRowKeys.push(key);
          }
        }
        allRowKeys = (0, _array.pullAll)(allRowKeys, [...disabledRowKeys, ...curSelectedRowKeys]);
        changedRowKeys = [...allRowKeys];
      } else {
        selectedRowKeys = (0, _array.pullAll)(selectedRowKeys, allRowKeys);
        changedRowKeys = [...curSelectedRowKeys];
      }
      const changedRows = this.getSelectedRows(null, changedRowKeys || []);
      const selectedRows = this.getSelectedRows(null, selectedRowKeys || []);
      if (!this._selectionIsControlled()) {
        this._adapter.setSelectedRowKeys(selectedRowKeys);
      }
      this._adapter.notifySelectAll(selected, selectedRows, changedRows, e);
      this._adapter.notifySelectionChange(selectedRowKeys, selectedRows);
    }
  }
  /**
   * row keys => rows
   * @param {*} dataSource
   * @param {*} selectedRowKeys
   * @param {*} selectedRowKeysSet Recursive optimization
   */
  getSelectedRows(dataSource, selectedRowKeys, selectedRowKeysSet) {
    dataSource = dataSource == null ? this._getDataSource() : dataSource;
    selectedRowKeys = selectedRowKeys == null ? this._getSelectedRowKeys() : selectedRowKeys;
    if (!(0, _isSet2.default)(selectedRowKeysSet)) {
      selectedRowKeysSet = new Set(selectedRowKeys);
    }
    const childrenRecordName = this.getProp('childrenRecordName');
    const selectedRows = [];
    if ((0, _isSet2.default)(selectedRowKeysSet) && selectedRowKeysSet.size && Array.isArray(dataSource) && dataSource.length) {
      // Time complexity optimization, replace the includes operation of array with has of set
      selectedRows.push(...dataSource.filter(data => selectedRowKeysSet.has(this.getRecordKey(data))));
      if (selectedRows.length < selectedRowKeys.length) {
        for (const item of dataSource) {
          const children = (0, _get2.default)(item, childrenRecordName);
          if (Array.isArray(children) && children.length) {
            const rows = this.getSelectedRows(children, selectedRowKeys, selectedRowKeysSet);
            selectedRows.push(...rows);
          }
        }
      }
    }
    return selectedRows;
  }
  getAllDisabledRowKeys(dataSource, getCheckboxProps) {
    dataSource = dataSource == null ? this._getDataSource() : dataSource;
    getCheckboxProps = getCheckboxProps == null ? (0, _get2.default)(this.getProp('rowSelection'), 'getCheckboxProps') : getCheckboxProps;
    const childrenRecordName = this.getProp('childrenRecordName');
    const disabledRowKeys = [];
    if (Array.isArray(dataSource) && dataSource.length && typeof getCheckboxProps === 'function') {
      for (const record of dataSource) {
        const props = getCheckboxProps(record);
        if (props && props.disabled) {
          disabledRowKeys.push(this.getRecordKey(record));
        }
        const children = (0, _get2.default)(record, childrenRecordName);
        if (Array.isArray(children) && children.length) {
          const keys = this.getAllDisabledRowKeys(children, getCheckboxProps);
          disabledRowKeys.push(...keys);
        }
      }
    }
    return disabledRowKeys;
  }
  getAllRowKeys(dataSource) {
    dataSource = dataSource == null ? this._getDataSource() : dataSource;
    const childrenRecordName = this.getProp('childrenRecordName');
    const allRowKeys = [];
    if (Array.isArray(dataSource) && dataSource.length) {
      for (const record of dataSource) {
        const childrenRowKeys = [];
        const children = (0, _get2.default)(record, childrenRecordName);
        if (Array.isArray(children) && children.length) {
          childrenRowKeys.push(...this.getAllRowKeys(children));
        }
        allRowKeys.push(this.getRecordKey(record), ...childrenRowKeys);
      }
    }
    return allRowKeys;
  }
  /**
   * Check if the selected item is in allRowKeysSet
   * @param {Array} selectedRowKeys
   * @param {Set} allRowKeysSet
   */
  hasRowSelected(selectedRowKeys, allRowKeysSet) {
    return Boolean(Array.isArray(selectedRowKeys) && selectedRowKeys.length && (0, _isSet2.default)(allRowKeysSet) && allRowKeysSet.size && selectedRowKeys.filter(key => allRowKeysSet.has(key)).length);
  }
  /**
   * expand processing function
   * @param {Boolean} expanded
   * @param {String} realKey
   * @param {Event} domEvent
   */
  handleRowExpanded(expanded, realKey, domEvent) {
    this.stopPropagation(domEvent);
    const expandedRowKeys = [...this.getState('expandedRowKeys')];
    const index = expandedRowKeys.indexOf(realKey);
    const keyIsValid = typeof realKey === 'string' || typeof realKey === 'number';
    if (keyIsValid && expanded && index === -1) {
      expandedRowKeys.push(realKey);
    } else if (keyIsValid && !expanded && index > -1) {
      expandedRowKeys.splice(index, 1);
    }
    if (!this._rowExpansionIsControlled()) {
      this._adapter.setExpandedRowKeys(expandedRowKeys);
    }
    const expandedRows = this.getSelectedRows(null, expandedRowKeys);
    let expandedRow = this.getSelectedRows(null, [realKey])[0];
    // groups record processing
    const groups = this._getGroups();
    if (groups) {
      // Construct group expandRow
      if (groups.has(realKey)) {
        expandedRow = {
          groupKey: realKey
        };
      }
      // If expandedRowKeys includes groupKey, add to expandedRows
      for (let i = 0, len = expandedRowKeys.length; i < len; i++) {
        if (groups.has(realKey)) {
          expandedRows.push({
            groupKey: expandedRowKeys[i]
          });
        }
      }
    }
    this._adapter.notifyExpand(expanded, expandedRow, domEvent);
    this._adapter.notifyExpandedRowsChange(expandedRows);
  }
  /**
   * get state.groups
   * @returns {Map|Null}
   */
  _getGroups() {
    const groupBy = this._adapter.getProp('groupBy');
    if (groupBy !== null) {
      const groups = this._adapter.getState('groups');
      return groups;
    }
    return null;
  }
  /**
   * Determine whether you have selected all except for disabled
   * @param {Set} selectedRowKeysSet
   * @param {Set} disabledRowKeysSet
   * @param {Array} allKeys keys after sorted and filtered
   */
  allIsSelected(selectedRowKeysSet, disabledRowKeysSet, allKeys) {
    const filteredAllKeys = (0, _filter2.default)(allKeys, key => key != null && !disabledRowKeysSet.has(key));
    if (filteredAllKeys && filteredAllKeys.length) {
      for (const key of filteredAllKeys) {
        if (key != null && !selectedRowKeysSet.has(key)) {
          return false;
        }
      }
      return true;
    } else {
      const isAllSelected = allKeys.length && allKeys.every(rowKey => selectedRowKeysSet.has(rowKey));
      return isAllSelected || false;
    }
  }
  /**
   * This function is not used yet
   * @param {*} selectedRowKeys
   * @param {*} allKeys
   */
  allIsNotSelected(selectedRowKeys, allKeys) {
    for (const key of allKeys) {
      if (key != null && Array.isArray(selectedRowKeys) && selectedRowKeys.includes(key)) {
        return true;
      }
    }
    return false;
  }
  formatPaginationInfo() {
    let pagination = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    let defaultPageText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
    let info = '';
    const formatPageText = (0, _get2.default)(this.getProp('pagination'), 'formatPageText');
    const {
      total,
      pageSize,
      currentPage
    } = pagination;
    const currentStart = Math.min((currentPage - 1) * pageSize + 1, total);
    const currentEnd = Math.min(currentPage * pageSize, total);
    if (formatPageText || formatPageText !== false && defaultPageText && total > 0) {
      info = typeof formatPageText === 'function' ? formatPageText({
        currentStart,
        currentEnd,
        total
      }) : defaultPageText.replace('${currentStart}', currentStart).replace('${currentEnd}', currentEnd).replace('${total}', total);
    }
    return info;
  }
  toggleShowFilter(dataIndex, visible) {
    let filterObj = this.getQuery(dataIndex);
    const filterDropdownVisible = visible;
    filterObj = Object.assign(Object.assign({}, filterObj), {
      filterDropdownVisible
    });
    if (!this._filterShowIsControlled()) {
      // this._adapter.setQuery({
      //     ...filterObj,
      //     filterDropdownVisible,
      // });
    }
    this._adapter.notifyFilterDropdownVisibleChange(filterDropdownVisible, dataIndex);
  }
  /**
   * Called when the filter changes
   * @param {*} dataIndex
   * @param {*} data
   */
  handleFilterSelect(dataIndex) {
    let data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    let query = this.getQuery(dataIndex);
    let queries = [...this._adapter.getState('queries')];
    const {
      filteredValue
    } = data;
    query = Object.assign(Object.assign({}, query), {
      filteredValue
    });
    queries = (0, _utils.mergeQueries)(query, queries);
    const mergedQueries = this._mergeToQueries(query, null);
    const filters = this._getAllFilters(mergedQueries);
    if (!this._filterIsControlled(dataIndex)) {
      this._adapter.setQueries(queries);
      this.handleClickFilterOrSorter(queries);
    }
    this._notifyChange(null, filters, undefined, {
      changeType: 'filter'
    });
  }
  /**
   * Click the sort button to call
   * @param {*} column
   * @param {*} e
   */
  handleSort() {
    let column = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    let e = arguments.length > 1 ? arguments[1] : undefined;
    let check = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    var _a, _b, _c, _d;
    this.stopPropagation(e);
    /* if mouse down to the resizable handle, do not trigger the sorting，fix #2802
        The target of the click event may be different from the target of the mousedown,
        e.g: Press the mouse, move to another node and then release it，
        So according to the event in the mousedown to determine whether to trigger the sorting
    */
    if (check) {
      if (this.mouseDownTarget && ((_a = this.mouseDownTarget) === null || _a === void 0 ? void 0 : _a.targetName) === 'SPAN' && ((_d = (_c = (_b = this.mouseDownTarget) === null || _b === void 0 ? void 0 : _b.className) === null || _c === void 0 ? void 0 : _c.includes) === null || _d === void 0 ? void 0 : _d.call(_c, 'react-resizable-handle'))) {
        return;
      }
      this.mouseDownTarget = null;
    }
    const {
      dataIndex
    } = column;
    let queries = this.getState('queries');
    let curQuery = null;
    queries = [...queries];
    (0, _each2.default)(queries, (query, idx, arr) => {
      if (query.sorter) {
        const sorterObj = Object.assign({}, query);
        const stateSortOrder = (0, _get2.default)(sorterObj, 'sortOrder');
        const defaultSortOrder = (0, _get2.default)(sorterObj, 'defaultSortOrder', false);
        let querySortOrder = this.isSortOrderValid(stateSortOrder) ? stateSortOrder : defaultSortOrder;
        if (dataIndex && dataIndex === sorterObj.dataIndex) {
          if (querySortOrder === _constants.strings.SORT_DIRECTIONS[0]) {
            querySortOrder = _constants.strings.SORT_DIRECTIONS[1];
          } else if (querySortOrder === _constants.strings.SORT_DIRECTIONS[1]) {
            querySortOrder = false;
          } else {
            querySortOrder = _constants.strings.SORT_DIRECTIONS[0];
          }
        } else {
          // This results in the current click only supports single column sorting
          querySortOrder = false;
        }
        arr[idx] = Object.assign(Object.assign({}, sorterObj), {
          sortOrder: querySortOrder
        });
        if (dataIndex === sorterObj.dataIndex) {
          curQuery = arr[idx];
        }
      }
    });
    if (!this._sorterIsControlled(dataIndex)) {
      this._adapter.setQueries(queries);
      this.handleClickFilterOrSorter(queries);
    }
    // notify sort event
    this._notifyChange(null, null, curQuery, {
      changeType: 'sorter'
    });
  }
  /**
   * Recalculate the cached data after clicking filter or sorter
   * @param {*} queries
   */
  handleClickFilterOrSorter(queries) {
    const dataSource = [...this.getProp('dataSource')];
    const sortedDataSource = this.getFilteredSortedDataSource(dataSource, queries);
    const allDataDisabledRowKeys = this.getAllDisabledRowKeys(sortedDataSource);
    this.setCachedFilteredSortedDataSource(sortedDataSource);
    this.setAllDisabledRowKeys(allDataDisabledRowKeys);
    const pageData = this.getCurrentPageData(sortedDataSource);
    this.setAdapterPageData(pageData);
  }
  getQuery(dataIndex, queries) {
    queries = queries || this.getState('queries');
    if (dataIndex != null) {
      return (0, _find2.default)(queries, query => query.dataIndex === dataIndex);
    }
    return undefined;
  }
  getCellWidths(flattenedColumns, flattenedWidths, ignoreScrollBarKey) {
    return this._adapter.getCellWidths(flattenedColumns, flattenedWidths, ignoreScrollBarKey);
  }
  setHeadWidths(headWidths, index) {
    return this._adapter.setHeadWidths(headWidths, index);
  }
  getHeadWidths(index) {
    return this._adapter.getHeadWidths(index);
  }
  mergedRowExpandable(record) {
    return this._adapter.mergedRowExpandable(record);
  }
  setBodyHasScrollbar(bodyHasScrollbar) {
    this._adapter.setBodyHasScrollbar(bodyHasScrollbar);
  }
}
var _default = exports.default = TableFoundation;