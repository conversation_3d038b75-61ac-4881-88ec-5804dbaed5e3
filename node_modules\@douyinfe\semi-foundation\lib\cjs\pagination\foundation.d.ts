import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface PaginationAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    setPageList: (pageListState: AdapterPageList) => void;
    setDisabled: (prevIsDisabled: boolean, nextIsDisabled: boolean) => void;
    updateTotal: (total: number) => void;
    updatePageSize: (pageSize: number) => void;
    updateQuickJumpPage: (quickJumpPage: string | number) => void;
    updateAllPageNumbers: (allPageNumbers: number[]) => void;
    setCurrentPage: (pageIndex: number) => void;
    registerKeyDownHandler: (handler: KeyDownHandler) => void;
    unregisterKeyDownHandler: (handler: KeyDownHandler) => void;
    notifyPageChange: (pageIndex: number) => void;
    notifyPageSizeChange: (pageSize: number) => void;
    notifyChange: (pageIndex: number, pageSize: number) => void;
}
export type PageRenderText = number | '...';
export type PageList = PageRenderText[];
export interface AdapterPageList {
    pageList: PageRenderText[];
    restLeftPageList: number[];
    restRightPageList: number[];
}
export type KeyDownHandler = any;
declare class PaginationFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<PaginationAdapter<P, S>, P, S> {
    constructor(adapter: PaginationAdapter<P, S>);
    init(): void;
    destroy(): void;
    _registerEventHandler(): void;
    _unregisterEventHandler(): void;
    _updateDisabled(pageInfo: {
        currentPage: number;
        total: number;
        pageSize: number;
    }): void;
    goPage(targetPageIndex: number | '...'): void;
    updatePage(targetPageIndex?: number, total?: number, pageSize?: number): void;
    updateAllPageNumbers(total: number, pageSize: number): void;
    goPrev(): void;
    goNext(): void;
    _updatePageList(pageListInfo: {
        currentPage: number;
        total: number;
        pageSize: number;
    }): void;
    changePageSize(newPageSize: number): void;
    handleKeyDown(): void;
    pageSizeInOpts(): any[];
    handleQuickJumpNumberChange(targetPage: string | number): void;
    _handleQuickJump(quickJumpPage: string | number): void;
    handleQuickJumpBlur(): void;
    handleQuickJumpEnterPress(targetPage: any): void;
    _getTotalPageNumber(total: number, pageSize: number): number;
}
export default PaginationFoundation;
