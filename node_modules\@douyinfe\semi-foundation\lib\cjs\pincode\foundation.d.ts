import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface PinCodeBaseProps {
    disabled?: boolean;
    value?: string;
    format?: "number" | "mixed" | RegExp | ((value: string) => boolean);
    onChange: (value: string) => void;
    defaultValue?: string;
    count?: number;
    autoFocus?: boolean;
    onComplete?: (value: string) => void;
}
export interface PinCodeBaseState {
    valueList: string[];
    currentActiveIndex: number;
}
export interface PinCodeAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    onCurrentActiveIndexChange: (index: number) => Promise<void> | void;
    notifyValueChange: (values: string[]) => void;
    changeSpecificInputFocusState: (index: number, state: "blur" | "focus") => void;
    updateValueList: (newValueList: PinCodeBaseState['valueList']) => Promise<void> | void;
}
declare class PinCodeFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<PinCodeAdapter<P, S>, P, S> {
    constructor(adapter: PinCodeAdapter<P, S>);
    static numberReg: RegExp;
    static mixedReg: RegExp;
    handleCurrentActiveIndexChange: (index: number, state: "focus" | "blur") => void;
    completeSingleInput: (i: number, singleInputValue: string) => Promise<void>;
    validateValue: (value?: string) => boolean;
    updateValueList: (newValueList: PinCodeBaseState['valueList']) => Promise<void>;
    handlePaste: (e: ClipboardEvent, startInputIndex: number) => Promise<void>;
    handleKeyDownOnSingleInput: (e: KeyboardEvent, index: number) => void;
}
export default PinCodeFoundation;
