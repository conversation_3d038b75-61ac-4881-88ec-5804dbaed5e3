import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import noop from './utils/noop';
export interface StyledAnimationProps {
    className?: string;
    type?: any;
    style?: React.CSSProperties;
    speed?: string | number;
    delay?: string | number;
    reverse?: boolean | string;
    loop?: string | number;
    children?: any;
    onStart?: (value: any) => void;
    onFrame?: (value: any) => void;
    onRest?: (value: any) => void;
    prefixCls?: string;
    timing?: string;
    duration?: string | number;
    fillMode?: string;
}
export interface StyledAnimateStyleType {
    animationTimingFunction: string;
    animationName: any;
    animationDuration: string | number;
    animationDelay: string | number;
    animationIterationCount: string | number;
    animationDirection: string;
    animationFillMode: string;
}
export default class StyledAnimation extends PureComponent<StyledAnimationProps> {
    static propTypes: {
        className: PropTypes.Requireable<string>;
        type: PropTypes.Requireable<any>;
        speed: PropTypes.Requireable<NonNullable<string | number>>;
        delay: PropTypes.Requireable<NonNullable<string | number>>;
        reverse: PropTypes.Requireable<NonNullable<string | boolean>>;
        loop: PropTypes.Requireable<NonNullable<string | number>>;
        children: PropTypes.Requireable<any>;
        onStart: PropTypes.Requireable<(...args: any[]) => any>;
        onFrame: PropTypes.Requireable<(...args: any[]) => any>;
        onRest: PropTypes.Requireable<(...args: any[]) => any>;
        prefixCls: PropTypes.Requireable<string>;
        timing: PropTypes.Requireable<string>;
        duration: PropTypes.Requireable<NonNullable<string | number>>;
        fillMode: PropTypes.Requireable<string>;
    };
    static defaultProps: {
        prefixCls: string;
        speed: string;
        onFrame: typeof noop;
        onStart: typeof noop;
        onRest: typeof noop;
    };
    constructor(props?: {});
    _generateAnimateEvents: (child: React.ReactElement, props?: StyledAnimationProps) => {
        onAnimationIteration: (...args: any) => void;
        onAnimationStart: (...args: any) => void;
        onAnimationEnd: (...args: any) => void;
    };
    _hasSpeedClass: (speed?: string | number) => boolean;
    _hasTypeClass: (type?: any) => any;
    _hasDelayClass: (delay?: string | number) => boolean;
    _hasLoopClass: (loop?: string | number) => boolean;
    render(): any;
}
