declare const cssClasses: {
    readonly PREFIX: "semi-tooltip";
};
declare const strings: {
    readonly POSITION_SET: readonly ["top", "topLeft", "topRight", "left", "leftTop", "leftBottom", "right", "rightTop", "rightBottom", "bottom", "bottomLeft", "bottomRight", "leftTopOver", "rightTopOver", "leftBottomOver", "rightBottomOver"];
    readonly TRIGGER_SET: readonly ["hover", "focus", "click", "custom", "contextMenu"];
    readonly STATUS_DISABLED: "disabled";
    readonly STATUS_LOADING: "loading";
};
declare const numbers: {
    readonly ARROW_BOUNDING: {
        readonly offsetX: 0;
        readonly offsetY: 2;
        readonly width: 24;
        readonly height: 7;
    };
    readonly DEFAULT_Z_INDEX: 1060;
    readonly MOUSE_ENTER_DELAY: 50;
    readonly MOUSE_LEAVE_DELAY: 50;
    readonly SPACING: 8;
    readonly MARGIN: 0;
};
export { cssClasses, strings, numbers };
