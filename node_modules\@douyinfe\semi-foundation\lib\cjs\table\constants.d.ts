declare const cssClasses: {
    readonly PREFIX: "semi-table";
    readonly TABLE: "semi-table";
    readonly WRAP: "semi-table-wrapper";
    readonly THEAD: "semi-table-thead";
    readonly TR: "semi-table-row";
    readonly TH: "semi-table-row-head";
    readonly TD: "semi-table-row-cell";
};
declare const strings: {
    readonly SIZE_SMALL: "small";
    readonly SIZE_MIDDLE: "middle";
    readonly SIZES: readonly ["small", "default", "middle"];
    readonly LAYOUTS: readonly ["", "auto", "fixed"];
    readonly PAGINATION_POSITIONS: readonly ["bottom", "top", "both"];
    readonly FOOTER_POSITIONS: readonly [false, true, "left", "right"];
    readonly SORT_DIRECTIONS: readonly ["ascend", "descend"];
    readonly FIXED_SET: readonly [false, true, "left", "right"];
    readonly ALIGNS: readonly ["left", "right", "center"];
    readonly JUSTIFY_CONTENT: readonly ["flex-start", "flex-end", "center"];
    readonly SCROLL_HORIZONTAL_POSITIONS: readonly ["left", "middle", "right"];
    readonly DEFAULT_KEY_COLUMN_SELECTION: "column-selection";
    readonly DEFAULT_KEY_COLUMN_EXPAND: "column-expand";
    readonly DEFAULT_KEY_COLUMN_TITLE: "column-title";
    readonly DEFAULT_KEY_COLUMN_SORTER: "column-sorter";
    readonly DEFAULT_KEY_COLUMN_FILTER: "column-filter";
    readonly DEFAULT_KEY_COLUMN_SCROLLBAR: "column-scrollbar";
    readonly DEFAULT_COMPONENTS: {
        readonly table: "table";
        readonly header: {
            readonly outer: "table";
            readonly wrapper: "thead";
            readonly row: "tr";
            readonly cell: "th";
        };
        readonly body: {
            readonly outer: "table";
            readonly wrapper: "tbody";
            readonly row: "tr";
            readonly cell: "td";
        };
        readonly footer: {
            readonly wrapper: "tfoot";
            readonly row: "tr";
            readonly cell: "td";
        };
    };
    readonly EXPAND_RELATED_PROPS: readonly ["expandedRowRender", "dataSource", "hideExpandedColumn", "childrenRecordName", "rowExpandable"];
};
declare const numbers: {
    readonly DEFAULT_PAGE_SIZE: 10;
    readonly DEFAULT_WIDTH_COLUMN_EXPAND: 60;
    readonly DEFAULT_WIDTH_COLUMN_SELECTION: 60;
    readonly DEFAULT_INDENT_WIDTH: 20;
    readonly DEFAULT_CELL_PADDING_LEFT: 16;
    readonly DEFAULT_CELL_PADDING_RIGHT: 16;
    readonly DEFAULT_CELL_PADDING_TOP: 16;
    readonly DEFAULT_CELL_PADDING_BOTTOM: 16;
    readonly DEFAULT_CELL_MIDDLE_PADDING_TOP: 12;
    readonly DEFAULT_CELL_MIDDLE_PADDING_BOTTOM: 12;
    readonly DEFAULT_CELL_SMALL_PADDING_TOP: 8;
    readonly DEFAULT_CELL_SMALL_PADDING_BOTTOM: 8;
    readonly DEFAULT_CELL_BORDER_WIDTH_LEFT: 1;
    readonly DEFAULT_CELL_BORDER_WIDTH_RIGHT: 1;
    readonly DEFAULT_CELL_BORDER_WITH_BOTTOM: 1;
    readonly DEFAULT_CELL_LINE_HEIGHT: 20;
    readonly DEFAULT_VIRTUALIZED_BODY_HEIGHT: 600;
    readonly DEFAULT_VIRTUALIZED_SECTION_ROW_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_MIN_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_SECTION_ROW_MIDDLE_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_MIDDLE_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_MIDDLE_MIN_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_SECTION_ROW_SMALL_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_SMALL_HEIGHT: number;
    readonly DEFAULT_VIRTUALIZED_ROW_SMALL_MIN_HEIGHT: number;
};
export { cssClasses, strings, numbers };
