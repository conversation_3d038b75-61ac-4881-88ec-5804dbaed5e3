"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sortDate = exports.round = exports.renderDailyEvent = exports.parseWeeklyAllDayEvent = exports.parseRangeAllDayEvent = exports.parseEvent = exports.parseAllDayEvent = exports.isAllDayEvent = exports.getPos = exports.getCurrDate = exports.filterWeeklyEvents = exports.filterEvents = exports.convertEventsArrToMap = exports.collectDailyEvents = exports.checkWeekend = exports.calcWeekData = exports.calcRowHeight = exports.calcRangeData = exports.amendEvent = void 0;
var _dateFns = require("date-fns");
const copyEvent = function (event, date, start, end) {
  let allDay = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
  const copied = Object.assign({}, event);
  copied.date = date;
  start ? copied.start = start : null;
  end ? copied.end = end : null;
  copied.allDay = allDay;
  return copied;
};
const isDateInRange = (dirtyDate, dirtyStart, dirtyEnd) => {
  const date = (0, _dateFns.toDate)(dirtyDate);
  const start = (0, _dateFns.toDate)(dirtyStart);
  const end = (0, _dateFns.toDate)(dirtyEnd);
  return date.getTime() < end.getTime() && date.getTime() >= start.getTime();
};
const sortDate = (a, b) => {
  const res = (0, _dateFns.isBefore)(new Date(a), new Date(b)) ? -1 : 1;
  return res;
};
exports.sortDate = sortDate;
const checkWeekend = val => (0, _dateFns.isWeekend)(val);
exports.checkWeekend = checkWeekend;
const getCurrDate = () => new Date();
exports.getCurrDate = getCurrDate;
const round = value => Math.round(value * 1000) / 1000;
exports.round = round;
const getPos = value => {
  const currSec = ((0, _dateFns.getHours)(value) * 60 + (0, _dateFns.getMinutes)(value)) * 60 + (0, _dateFns.getSeconds)(value);
  const totalSec = 24 * 60 * 60;
  return currSec / totalSec;
};
exports.getPos = getPos;
const isAllDayEvent = event => 'allDay' in event && event.allDay;
/**
 *
 * @param {object} event
 * normalize event object:
 * if event object does not have start time, add start time = end time - 1h; if not same day, then startday of the end
 * if event object does not have end time, add end time = start time + 1h; if not same day, then endday of the start
 */
exports.isAllDayEvent = isAllDayEvent;
const amendEvent = event => {
  const {
    start,
    end
  } = event;
  if (!start && !end) {
    return undefined;
  } else if (!start) {
    event.start = (0, _dateFns.isSameDay)(end, (0, _dateFns.addHours)(end, -1)) ? (0, _dateFns.addHours)(end, -1) : (0, _dateFns.startOfDay)(end);
  } else {
    event.end = (0, _dateFns.isSameDay)(start, (0, _dateFns.addHours)(start, 1)) ? (0, _dateFns.addHours)(start, 1) : (0, _dateFns.endOfDay)(start);
  }
  return event;
};
/**
 *
 * @param {arr} events
 * find the max topInd and used as row height
 */
exports.amendEvent = amendEvent;
const calcRowHeight = events => {
  const topIndArr = events.map(item => item.topInd);
  return topIndArr.length ? Math.max(...topIndArr) + 1 : 1;
};
exports.calcRowHeight = calcRowHeight;
const calcRangeData = (value, start, rangeLen, mode, locale, weekStartsOn) => {
  const today = getCurrDate();
  const arr = [];
  [...Array(rangeLen).keys()].map(ind => {
    const dateObj = {};
    const date = (0, _dateFns.addDays)(start, ind);
    dateObj.ind = ind;
    dateObj.date = date;
    dateObj.dayString = (0, _dateFns.format)(date, 'd', {
      locale,
      weekStartsOn
    });
    dateObj.weekday = (0, _dateFns.format)(date, 'EEE', {
      locale,
      weekStartsOn
    });
    dateObj.isToday = (0, _dateFns.isSameDay)(date, today);
    dateObj.isWeekend = checkWeekend(date);
    if (mode === 'month') {
      dateObj.isSameMonth = (0, _dateFns.isSameMonth)(value, date);
      dateObj.month = (0, _dateFns.format)(date, 'LLL', {
        locale
      });
    }
    arr.push(dateObj);
  });
  return arr;
};
/**
 *
 * @param {Date} date
 * @param {Date} monthStart current month start date, using for month mode
 * @param {string} mode
 * @param {string} locale
 * @returns {object[]} { date: Date, dayString: string, ind: number, isToday: boolean, isWeekend: boolean, weekday: string }
 * create weekly object array
 */
exports.calcRangeData = calcRangeData;
const calcWeekData = function (value, monthStart) {
  let mode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'week';
  let locale = arguments.length > 3 ? arguments[3] : undefined;
  let weekStartsOn = arguments.length > 4 ? arguments[4] : undefined;
  const start = (0, _dateFns.startOfWeek)(value, {
    weekStartsOn
  });
  const realValue = monthStart || value;
  return calcRangeData(realValue, start, 7, mode, locale, weekStartsOn);
};
/**
 *
 * @param {object} event
 * @param {boolean} allDay
 * @returns {object[]} { allDay: boolean, data: Date, start: Date, end: Date, children: ReactNode }
 * parsed a spanned all-day event into multiple dates
 */
exports.calcWeekData = calcWeekData;
const parseAllDayEvent = function (event) {
  let allDay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
  let currDate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;
  const res = [];
  const {
    start,
    end
  } = event;
  if (start && end) {
    const diff = (0, _dateFns.differenceInCalendarDays)(end, start);
    [...Array(diff + 1).keys()].map(day => {
      res.push(copyEvent(event, (0, _dateFns.addDays)(start, day), null, null, allDay));
    });
  } else {
    const date = start || end || currDate;
    res.push(copyEvent(event, (0, _dateFns.startOfDay)(date), null, null, allDay));
  }
  return res;
};
/**
 *
 * @param {object} event
 * @returns {object[]} { allDay: boolean, data: Date, start: Date, end: Date, children: ReactNode }
 * parsed events
 */
exports.parseAllDayEvent = parseAllDayEvent;
const parseEvent = event => {
  const {
    start,
    end
  } = event;
  let res = [];
  if (isAllDayEvent(event)) {
    return parseAllDayEvent(event);
  }
  if (start && end) {
    if (!(0, _dateFns.isBefore)(start, end)) {
      [event.start, event.end] = [event.end, event.start];
    }
    if ((0, _dateFns.isSameDay)(start, end)) {
      res.push(copyEvent(event, (0, _dateFns.startOfDay)(start)));
    } else if (Math.abs((0, _dateFns.differenceInHours)(start, end)) < 24) {
      res.push(copyEvent(event, (0, _dateFns.startOfDay)(start), null, (0, _dateFns.endOfDay)(start)));
      res.push(copyEvent(event, (0, _dateFns.startOfDay)(end), (0, _dateFns.startOfDay)(end)));
    } else {
      res = res.concat(parseAllDayEvent(event));
    }
  } else {
    const amend = amendEvent(event);
    res.push(copyEvent(amend, (0, _dateFns.startOfDay)(amend.start)));
  }
  return res;
};
/**
 *
 * @param {arr} arr
 * @param  {key}
 * @param {function} func callback function
 * @returns {map}
 * convert events array to may, use datestring as key
 */
exports.parseEvent = parseEvent;
const convertEventsArrToMap = (arr, key, func, displayValue) => {
  const res = new Map();
  arr.forEach(item => {
    let val;
    if (key in item) {
      val = item[key];
    } else {
      val = (0, _dateFns.startOfDay)(displayValue);
    }
    const k = func ? func(val).toString() : val.toString();
    if (res.has(k)) {
      res.get(k).push(item);
    } else {
      res.set(k, [item]);
    }
  });
  return res;
};
/**
 * @returns {arr}
 * filter out event that is not in the date range
 */
exports.convertEventsArrToMap = convertEventsArrToMap;
const filterEvents = (events, start, end) => {
  const res = new Map();
  [...events.keys()].map(day => {
    const item = events.get(day);
    const date = new Date(day);
    if (isDateInRange(date, start, end)) {
      if (res.has(day)) {
        res.set(day, [...res.get(day), ...item]);
      } else {
        res.set(day, item);
      }
    } else if ((0, _dateFns.isBefore)(end, date)) {
      // do nothing
    } else {
      const filtered = item.filter(i => !i.end || !(0, _dateFns.isBefore)(i.end, start));
      const key = start.toString();
      if (res.has(key)) {
        res.set(key, [...res.get(key), ...filtered]);
      } else {
        res.set(key, item);
      }
    }
  });
  return res;
};
/**
 * @returns {arr}
 * filter out event that is not in the week range
 */
exports.filterEvents = filterEvents;
const filterWeeklyEvents = (events, weekStart, weekStartsOn) => filterEvents(events, weekStart, (0, _dateFns.addDays)((0, _dateFns.endOfWeek)(weekStart, {
  weekStartsOn
}), 1));
/**
 * @returns {arr}
 * arrange and sort all day event for a range
 */
exports.filterWeeklyEvents = filterWeeklyEvents;
const parseRangeAllDayEvent = (event, startDate, rangeStart, rangeEnd, parsed) => {
  const dateRangeLen = (0, _dateFns.differenceInCalendarDays)(rangeEnd, rangeStart);
  event.sort((a, b) => sortDate(a.start, b.start)).forEach(item => {
    const itemInfo = Object.assign({}, item);
    const {
      end
    } = item;
    let dateLength;
    const j = (0, _dateFns.differenceInCalendarDays)(startDate, rangeStart);
    let i = 0;
    while (Boolean(parsed[i]) && Boolean(parsed[i][j])) {
      i++;
    }
    if (!end) {
      dateLength = 0;
    } else {
      dateLength = isDateInRange(end, rangeStart, rangeEnd) ? (0, _dateFns.differenceInCalendarDays)(end, startDate) : (0, _dateFns.differenceInCalendarDays)(rangeEnd, startDate);
    }
    itemInfo.leftPos = round(Number(j) / dateRangeLen);
    itemInfo.width = Math.min(1 - round(Number(j) / dateRangeLen), round((dateLength + 1) * 1 / dateRangeLen));
    itemInfo.topInd = i;
    [...Array(dateLength + 1).keys()].forEach(dist => {
      if (!parsed[i]) {
        parsed[i] = [];
      }
      if (dist > 0) {
        parsed[i][j + dist] = item;
      } else {
        parsed[i][j + dist] = itemInfo;
      }
    });
  });
  return parsed;
};
/**
 * @returns {arr}
 * arrange and sort weekly all day event
 */
exports.parseRangeAllDayEvent = parseRangeAllDayEvent;
const parseWeeklyAllDayEvent = (event, startDate, weekStart, parsed, weekStartsOn) => parseRangeAllDayEvent(event, startDate, weekStart, (0, _dateFns.addDays)((0, _dateFns.endOfWeek)(startDate, {
  weekStartsOn
}), 1), parsed);
exports.parseWeeklyAllDayEvent = parseWeeklyAllDayEvent;
const collectDailyEvents = events => {
  const collections = {};
  events.forEach((row, rowInd) => {
    row.forEach((event, ind) => {
      if (collections[ind]) {
        collections[ind][rowInd] = event;
      } else {
        collections[ind] = [];
        collections[ind][rowInd] = event;
      }
    });
  });
  return collections;
};
exports.collectDailyEvents = collectDailyEvents;
const renderDailyEvent = event => {
  let {
    start,
    end,
    allDay,
    children
  } = event;
  let startPos, endPos;
  if (isAllDayEvent(event)) {
    startPos = 0;
    endPos = 0;
  } else if (!start || !end) {
    const amend = amendEvent(event);
    endPos = getPos(amend.end);
    startPos = getPos(amend.start);
  } else {
    if (!(0, _dateFns.isBefore)(start, end)) {
      [start, end] = [end, start];
    }
    startPos = getPos(start);
    endPos = getPos(end);
  }
  const parsed = {
    startPos: round(startPos),
    endPos: round(endPos),
    children,
    allDay: Boolean(allDay)
  };
  return parsed;
};
exports.renderDailyEvent = renderDailyEvent;