// Size
$width-datepicker_day: 36px; // 日期格子尺寸
$width-datepicker_day_main: 32px; // 日期格子内容尺寸
$width-datepicker_month: $width-datepicker_day * 7; // 年月面板宽度
$width-datepicker_nav: 32px; // 导航高度
$height-datepicker_switch: 54px; // 日期时间切换高度
$height-datepicker_timeType_yam: calc(100% - 54px); // 时间面板高度
$height-datepicker_timeType_tpk: calc(100% - 54px); // 时间面板高度
$height-datepicker_panel_yam_scrolllist:  266px; // 时间滚动内容高度

$width-datepicker_monthPanel_max: 284px; // 年月选择器最大宽度
$width-datepicker_monthRangePanel_max: 384px; // 年月选择器最大宽度
$height-datepicker_timepicker_header_min: 24px; // 年月选择 header 最小高度
$width-datepicker_navigation_button_min: 32px; // header 按钮最小宽度
$height-datepicker_yamShowing_min: 378px; // 日期时间选择器菜单最小高度
$width-datepicker_yamShowing_min: 284px; // 选择器菜单最小宽度
$height-datepicker_dateType_yamShowing_min: 325px; // 日期选择器菜单最小高度
$width-datepicker_panel_yam_scrolllist_li_min: 64px; // 年月、时间滚动菜单项最小高度
$width-datepicker_presetPanel_left_and_right: 200px; // 左右方位快捷选择面板宽度
$height-datepicker_presetPanel_top_and_bottom_max: 100px; // 上下方位快捷选择面板最大高度
$width-datepicker_presetPanel_scroll_bar: 15px; // 快捷选择面板scrollbar宽度

$width-datepicker-border: $border-thickness-control; // 年月选择 header 底部分割线宽度
$width-datepicker_yam_header-borderRadius: var(--semi-border-radius-medium); // 年月选择 header 按钮圆角
$width-datepicker_day_main-borderRadius: var(--semi-border-radius-small); // 日期格子圆角
$width-datepicker_quick_control-borderRadius: var(--semi-border-radius-medium); // 快捷操作按钮圆角
$width-datepicker_slot-border: 1px; // 日期星期分割线宽度
$width-datepicker_range_input-border: $border-thickness-control-focus;
$width-datepicker_day_main-border: 1px;
$height-datepicker_timeType_insetInput_yam: 100%; // 时间面板高度 - 内嵌输入框
$height-datepicker_timeType_insetInput_tpk: 100%; // 时间面板高度 - 内嵌输入框
$width-datepicker_insetInput_date_type_wrapper: 284px; // 日期类型内嵌输入框宽度
$width-datepicker_insetInput_date_range_type_wrapper: $width-datepicker_insetInput_date_type_wrapper * 2; // 范围选择内嵌输入框宽度
$width-datepicker_insetInput_month_type_wrapper: 165px; // 月份类型内嵌输入框宽度
$width-datepicker_insetInput_month_range_type_wrapper: 331px; // 年月范围类型内嵌输入框宽度
$height-datepicker_insetInput_separator: 32px;
$height-datepicker_month_grid_yearType_insetInput: 317px;
$height-datepicker_month_grid_timeType_insetInput: 317px;
$width-datepicker_range_trigger-border: 0px; //  触发器边框宽度

// Spacing
$spacing-datepicker_day-marginX: ($width-datepicker_day - $width-datepicker_day_main) * 0.5; // 日期格子水平外边距
$spacing-datepicker_yam_header-paddingX: $spacing-base; // 年月选择 header 水平内边距
$spacing-datepicker_yam_header-paddingY: $spacing-base-tight; // 年月选择 header 垂直内边距
$spacing-datepicker_scrolllist_header-padding: $spacing-base; // 时间选择 header 内边距
$spacing-datepicker_scrolllist_body-padding: 0; // 时间选择滚动菜单内边距
$spacing-datepicker_footer-paddingTop: 10px; // 确认选择 footer 顶部内边距
$spacing-datepicker_footer-paddingBottom: 10px; // 确认选择 footer 底部内边距
$spacing-datepicker_footer-paddingRight: 8px; // 确认选择 footer 右侧内边距
$spacing-datepicker_footer_cancel_button-marginRight: 12px; // 确认选择 footer 取消按钮右外边距
$spacing-datepicker_footer_confirm_button-marginRight: 8px; // 确认选择 footer 确认按钮右外边距
$spacing-datepicker_navigation-paddingY: $spacing-base-tight; // 年月切换 header 垂直内边距
$spacing-datepicker_navigation-paddingX: $spacing-base; // 年月切换 header 水平内边距
$spacing-datepicker_month-padding: $spacing-base;
$spacing-datepicker_switch_datetime-paddingTop: $spacing-base; // 日期时间切换顶部内边距
$spacing-datepicker_switch_datetime-paddingBottom: $spacing-base; // 日期时间切换底部内边距
$spacing-datepicker_switch_text-paddingLeft: $spacing-tight; // 日期时间切换左侧内边距
$spacing-datepicker_quick_control_header-paddingTop: 18px;  // 快捷面板标题，上内边距
$spacing-datepicker_quick_control_top_and_bottom_content-paddingX: $spacing-base-loose; // 上下方位快捷操作面板, 左右内边距，默认20px
$spacing-datepicker_quick_control_content-paddingX: 12px; // 快捷面板内容，左右内边距
$spacing-datepicker_quick_control_content-marginTop: 14px; // 快捷面板内容，上边距
$spacing-datepicker_quick_control_item-margin: $spacing-tight; // 快捷操作面板按钮间距
$spacing-datepicker_range_input-paddingX: 8px;
$spacing-datepicker_range_input-paddingY: 3px;
$spacing-datepicker_range_input_inputWrapper_input-paddingY: 2px;
$spacing-datepicker_range_input_inputWrapper_input-paddingX: 4px;
$spacing-datepicker_range_input_prefix-paddingLeft: 12px;
$spacing-datepicker_range_input_prefix-paddingRight: 8px;
$spacing-datepicker_range_input_suffix-paddingLeft: 8px;
$spacing-datepicker_range_input_suffix-paddingRight: 12px;
$spacing-datepicker_range_input_clearbtn-paddingLeft: 8px;
$spacing-datepicker_range_input_clearbtn-paddingRight: 12px;
$spacing-datepicker_navigation_insetInput-paddingY: 8px; // 年月切换 header 垂直内边距 - 内嵌输入框
$spacing-datepicker_insetInput_wrapper-margin: 8px;
$spacing-datepicker_insetInput_wrapper-paddingY: 12px;
$spacing-datepicker_insetInput_wrapper-paddingX: 16px;
$spacing-datepicker_insetInput_wrapper-paddingBottom: 0;
$spacing-datepicker_insetInput_separator-paddingY: 0;
$spacing-datepicker_insetInput_separator-paddingX: 4px;
$spacing-scrollList_item_wheel_list_outer-paddingRight: 18px;

// Color
$color-datepicker_panel-bg-default: var(--semi-color-bg-3); // 日期选择器背景颜色
$color-datepicker_header-bg-default: var(--semi-color-bg-3); // 日期选择器 header 背景颜色
$color-datepicker_list-bg-default: var(--semi-color-bg-3); // 日期选择器滚动列表背景颜色
$color-datepicker_border-bg-default: var(--semi-color-border); // 日期选择器描边颜色
$color-datepicker_footer-bg-default: var(--semi-color-fill-0); // 日期选择器确认选择 footer 背景颜色
$color-datepicker_quick-bg-default: transparent; // 日期选择器快捷操作背景颜色
$color-datepicker_quick_button-text-default: var(--semi-color-primary); // 日期选择器快捷操作按钮文字颜色

$color-datepicker_day-text-default: var(--semi-color-text-2); // 日期时间切换文字颜色 - 默认
$color-datepicker_day-text-hover: var(--semi-color-fill-1); // 日期时间切换文字颜色 - 悬浮
$color-datepicker_day-text-active: var(--semi-color-text-0); // 日期时间切换文字颜色 - 选中

$color-datepicker_date-text-default: var(--semi-color-text-0); // 日期格子文字颜色 - 默认
$color-datepicker_date-text-hover: var(--semi-color-text-0); // 日期格子文字颜色 - 悬浮
$color-datepicker_date-text-active: var(--semi-color-text-0); // 日期格子文字颜色 - 按下
$color-datepicker_date-bg-default: var(--semi-color-fill-0); // 日期格子背景颜色 - 默认
$color-datepicker_date-bg-hover: var(--semi-color-fill-0); // 日期格子背景颜色 - 悬浮
$color-datepicker_date-bg-active: var(--semi-color-fill-1); // 日期格子背景颜色 - 按下

$color-datepicker_date_selected-text-default: var(--semi-color-white); // 日期格子文字颜色 - 选中
$color-datepicker_date_selected-bg-default: var(--semi-color-primary); // 日期格子背景颜色 - 选中

$color-datepicker_date_disabled-text-default: var(--semi-color-disabled-text); // 禁用日期格子文字颜色
$color-datepicker_date_disabled-bg-default: var(--semi-color-disabled-fill); // 禁用日期格子背景颜色

$color-datepicker_date_today-text-default: var(--semi-color-primary); // 今日文字颜色
$color-datepicker_date_today_disabled-text-default: var(--semi-color-primary-disabled); // 今日文字颜色 - 禁用

$color-datepicker_date_inHover-bg-default: var(--semi-color-primary-light-default); // 范围选择日期格子颜色
$color-datepicker_date_inRangeHover-bg-default: var(--semi-color-fill-0); // 年月面板菜单项背景色 - 悬浮
$color-datepicker_date_hoverDay-bg-default: var(--semi-color-fill-1); // 年月面板菜单项背景色 - 按下
$color-datepicker_date_selectedRange-bg-hover: var(--semi-color-primary-light-hover); // 范围选择日期格子颜色 - 悬浮
$color-datepicker_date_hoverday_range-bg-default: var(--semi-color-primary-light-active);
$color-datepicker_date_hoverday_around_single_selected-bg-default: var(--semi-color-primary-light-active);

$color-datepicker_nav_monthIcon-text-default: var(--semi-color-text-0); // 日期选择器 header 文字颜色
$color-datepicker_navIcon-text-default: var(--semi-color-text-2);  // 日期选择器 header 左右箭头颜色
$color-datepicker_input_primary-text-default: var(--semi-color-primary);
$color-datepicker_range_input-text-default: var(--semi-color-text-2);
$color-datepicker_range_input-bg-default: var(--semi-color-fill-0);
$color-datepicker_range_input-bg-hover: var(--semi-color-fill-1);
$color-datepicker_range_input-border-default: transparent;
$color-datepicker_range_input-border-active: var(--semi-color-focus-border);
$color-datepicker_range_input_warning-border-active: var(--semi-color-warning);
$color-datepicker_range_input_warning-bg-active: var(--semi-color-warning-light-hover);
$color-datepicker_range_input_warning-bg-default: var(--semi-color-warning-light-default);
$color-datepicker_range_input_warning-bg-hover: var(--semi-color-warning-light-hover);
$color-datepicker_range_input_warning-border-default: var(--semi-color-warning-light-default);
$color-datepicker_range_input_warning-border-hover: var(--semi-color-warning-light-hover);
$color-datepicker_range_input_error-border-default: var(--semi-color-danger-light-default);
$color-datepicker_range_input_error-bg-default: var(--semi-color-danger-light-default);
$color-datepicker_range_input_error-border-hover: var(--semi-color-danger-light-hover);
$color-datepicker_range_input_error-bg-hover: var(--semi-color-danger-light-hover);
$color-datepicker_range_input_error-border-active: var(--semi-color-danger);
$color-datepicker_range_input_error-bg-active: var(--semi-color-danger-light-hover);
$color-datepicker_range_input_disabled-text-default: var(--semi-color-disabled-text);
$color-datepicker_range_input_disabled-bg-default: var(--semi-color-disabled-fill);
$color-datepicker_range_input_disabled-bg-hover: var(--semi-color-disabled-fill);
$color-datepicker_range_input_clearbtn-icon-hover: var(--semi-color-primary-hover);
$color-datepicker_range_input_inputWrapper-border-default: transparent;
$color-datepicker_range_input_inputWrapper-bg-default: transparent;
$color-datepicker_range_input_inputWrapper-bg-focus: var(--semi-color-fill-1);
$color-datepicker_range_input_separator-text-active: var(--semi-color-text-0);
$color-datepicker_day_main-border: var(--semi-color-primary-active);
$color-datepicker_insetInput_separator: var(--semi-color-text-3);
$color-datepicker_range_trigger-border: transparent; // 范围日期选择模式触发器边框颜色
$color-datepicker_range_trigger-border-hover: transparent; // 范围日期选择模式触发器边框颜色 - 悬浮
$color-datepicker_range_trigger-border-active: transparent; // 范围日期选择模式触发器边框颜色 - 激活
$color-datepicker_range_trigger-border-focus: transparent; // 范围日期选择模式触发器边框颜色 - 聚焦



// Font
$font-datepicker_range_input_prefix_suffix_clearbtn-fontWeight: 600;
$font-datepicker_preset_header-fontWeight: 600;
$font-datepicker_range_input_prefix_suffix_clearbtn-fontSize: 14px;
$font-datepicker_range_input_prefix_suffix_clearbtn-lineHeight: 20px;
$font-datepicker_range_input_large-fontSize: 16px;


// compact 变量
$spacing-datepicker_month_compact-padding: 10px;

$width-datepicker_day_compact: 28px;
$width-datepicker_day_main_compact: 24px;
$width-datepicker_day_compact-borderRadius: 4px;
$width-datepicker_month_compact: $width-datepicker_day_compact * 7 + $spacing-datepicker_month_compact-padding * 2;
$width-datepicker_nav_compact: 24px;
$width-datepicker_panel_compact: 216px;
$height-datepicker_switch_compact: 32px;
$height-datepicker_tpk_compact: 256px;
$height-datepicker_yam_panel_compact: 256px;
$height-datepicker_yam_li_compact: 32px;
$height-datepicker_yam_panel_header_compact: 48px;
$radius-datepicker_range_input_inputWrapper: var(--semi-border-radius-small);
$height-datepicker_range_input-default: 32px;
$height-datepicker_range_input-small: 24px;
$height-datepicker_range_input-large: 40px;
$width-datepicker_insetInput_date_type_wrapper_compact: 216px;
$width-datepicker_insetInput_date_range_type_wrapper_compact: $width-datepicker_insetInput_date_type_wrapper_compact * 2;
$width-datepicker_insetInput_month_type_wrapper_compact: 195px;
$height-datepicker_insetInput_tpk_compact: 100%;
$height-datepicker_timeType_insetInput_yam_compact: 100%;
$height-datepicker_insetInput_wrapper_compact: 28px;

$lineHeight-datepicker_compact: 20px;
$lineHeight-datepicker_weekday_item_compact: 28px;
$height-datepicker_insetInput_compact: 26px;
$fontSize-datepicker_insetInput_compact-fontSize: 12px;

$spacing-datepicker_switch_compact-padding: 6px;
$spacing-datepicker_day_compact-margin: ($width-datepicker_day_compact - $width-datepicker_day_main_compact) * 0.5;
$spacing-datepicker_weeks_compact-padding: 10px;
$spacing-datepicker_weeks_compact-paddingTop: $spacing-tight - 2px;
$spacing-datepicker_weekday_compact-paddingLeft: 10px;
$spacing-datepicker_weekday_compact-paddingRight: 10px;
$spacing-datepicker_weekday_compact-paddingBottom: $spacing-tight;
$spacing-datepicker_compact-padding: 12px;
$spacing-datepicker_nav_compact-padding: 12px;
$spacing-datepicker_yam_panel_header_compact-padding: 12px;
$spacing-datepicker_footer_compact-paddintTop: 10px;
$spacing-datepicker_footer_compact-paddintBottom: 10px;
$spacing-datepicker_footer_compact-paddintRight: 8px;
$spacing-datepicker_scrolllist_shade_pre_compact-marginTop: -17px;
$spacing-datepicker_scrolllist_shade_post_compact-marginTop: 17px;
$spacing-datepicker_insetInput_wrapper_compact-margin: 4px;
$spacing-datepicker_insetInput_wrapper_compact-paddingY: 8px;
$spacing-datepicker_insetInput_wrapper_compact-paddingX: 8px;
$spacing-datepicker_insetInput_wrapper_compact-paddingBottom: 0;
$spacing-datepicker_insetInput_wrapper_rangeType_compact-paddingTop: 0;
$spacing-datepicker_quick_control_header_compact-paddingTop: 16px;  // compact, 快捷面板标题，上内边距
$spacing-datepicker_quick_control_content_compact-marginTop: 12px; // compact, 快捷面板内容，上边距
$spacing-datepicker_quick_control_left_and_right_content_compact-paddingX: 12px; // compact，左右方位，快捷面板内容，左右内边距
$spacing-datepicker_quick_control_top_and_bottom_content_compact-paddingX: 10px; // compact，上下方位，快捷面板内容，左右内边距

// Radius
$radius-datepicker_range_input: var(--semi-border-radius-small);

// Other
$transition-datepicker_range_input: background-color .16s ease-in-out;

// preset cacl
$width-datepicker_presetPanel_left_and_right_content: $width-datepicker_presetPanel_left_and_right - $spacing-datepicker_quick_control_content-paddingX * 2; // 左右方位快捷选择面板，内容宽度
$width-datepicker_presetPanel_top_and_bottom_content_date: $width-datepicker_day * 7 + $spacing-datepicker_month-padding * 2 - $spacing-datepicker_quick_control_top_and_bottom_content-paddingX * 2; // date/dateTime下， 上下方位快捷选择面板内容宽度， 默认（284 - 40）px
$width-datepicker_presetPanel_top_and_bottom_content_range: ($width-datepicker_day * 7 + $spacing-datepicker_month-padding * 2) * 2 - $spacing-datepicker_quick_control_top_and_bottom_content-paddingX * 2; // dateRange/dateTimeRange下， 上下方位快捷选择内容面板宽度，默认528px
$width-datepicker_presetPanel_top_and_bottom_content_month: 165px - $spacing-datepicker_quick_control_top_and_bottom_content-paddingX * 2; // month下，上下方位快捷选择内容面板宽度， 默认154px

$height-datepicker_month_max: $width-datepicker_day * 7 + 1px; // 年月面板最大高度, 最多6 + 1行，再加上一个border宽度, 默认253px
$height-datepicker_month_max_compact: $width-datepicker_day_compact * 7 + $spacing-datepicker_weeks_compact-paddingTop + $spacing-datepicker_weeks_compact-padding + $spacing-datepicker_weekday_compact-paddingBottom; // 年月面板最大高度, 最多6 + 1行，再加上padding，默认220px
$height-datepicker_date_panel: $height-datepicker_month_max + $spacing-datepicker_month-padding + $width-datepicker_nav + $spacing-datepicker_navigation-paddingY * 2; // date/dateRange，面板渲染最大高度，默认325px
$height-datepicker_date_time_panel: $height-datepicker_date_panel + $height-datepicker_switch - 1px; // dateTime/dateTImeRange 面板渲染最大高度. 默认378px
$height-datepicker_presetPanel_left_and_right_except_content: 20px + $spacing-datepicker_quick_control_header-paddingTop + $spacing-datepicker_quick_control_content-marginTop; // 除去content以外的高度，默认52px

// compact
$width-datepicker_presetPanel_top_and_bottom_content_date_compact: $width-datepicker_day_compact * 7 + $spacing-datepicker_weeks_compact-padding * 2 - $spacing-datepicker_quick_control_top_and_bottom_content_compact-paddingX * 2; // date/dateTime下， 上下方位快捷选择面板内容宽度， 默认（216 - 20）px
$width-datepicker_presetPanel_top_and_bottom_content_range_compact: ($width-datepicker_day_compact * 7 + $spacing-datepicker_weeks_compact-padding * 2) * 2 - $spacing-datepicker_quick_control_top_and_bottom_content_compact-paddingX * 2; // dateRange/dateTimeRange下， 上下方位快捷选择内容面板宽度，默认412px
$width-datepicker_presetPanel_top_and_bottom_content_month_compact: 165px - $spacing-datepicker_quick_control_top_and_bottom_content_compact-paddingX  * 2; // month下，上下方位快捷选择内容面板宽度， 默认174px

$height-datepicker_date_panel_compact: $height-datepicker_month_max_compact + $width-datepicker_nav_compact + $spacing-datepicker_nav_compact-padding; // compact，date/dateRange，面板渲染最大高度，默认256px
$height-datepicker_date_time_panel_compact: $height-datepicker_date_panel_compact + $height-datepicker_switch_compact; // compact，dateTime/dateTImeRange，面板渲染最大高度，默认288px
$height-datepicker_presetPanel_left_and_right_except_content_compact: 20px + $spacing-datepicker_quick_control_header_compact-paddingTop + $spacing-datepicker_quick_control_content_compact-marginTop; // compact，除去content以外的高度，默认48px

$width-datepicker_presetPanel_left_and_right_two_col_button: ($width-datepicker_presetPanel_left_and_right_content - $spacing-datepicker_quick_control_item-margin) * 0.5; // 左右方位快捷选择面板，固定两列，按钮宽度
$width-datepicker_presetPanel_top_and_bottom_three_col_button: ($width-datepicker_presetPanel_top_and_bottom_content_date - $spacing-datepicker_quick_control_item-margin * 2) * 0.333; // 上下方位快捷选择面板，固定三列，按钮宽度
$width-datepicker_presetPanel_top_and_bottom_five_col_button: ($width-datepicker_presetPanel_top_and_bottom_content_range - $spacing-datepicker_quick_control_item-margin * 4) * 0.2; // 上下方位快捷选择面板，固定五列，按钮宽度
$width-datepicker_presetPanel_top_and_bottom_two_col_button: ($width-datepicker_presetPanel_top_and_bottom_content_month - $spacing-datepicker_quick_control_item-margin) * 0.5; // 上下方位快捷选择面板，固定两列，按钮宽度

// compact
$width-datepicker_presetPanel_top_and_bottom_three_col_button_compact: ($width-datepicker_presetPanel_top_and_bottom_content_date_compact - $spacing-datepicker_quick_control_item-margin * 2) * 0.333; // 上下方位快捷选择面板，固定三列，按钮宽度
$width-datepicker_presetPanel_top_and_bottom_five_col_button_compact: ($width-datepicker_presetPanel_top_and_bottom_content_range_compact - $spacing-datepicker_quick_control_item-margin * 4) * 0.2; // 上下方位快捷选择面板，固定五列，按钮宽度
$width-datepicker_presetPanel_top_and_bottom_two_col_button_compact: ($width-datepicker_presetPanel_top_and_bottom_content_month_compact - $spacing-datepicker_quick_control_item-margin) * 0.5; // 上下方位快捷选择面板，固定两列，按钮宽度

// insetinput
$height-datepicker_inset_input: 32px + $spacing-datepicker_insetInput_wrapper-paddingY; // 默认尺寸，insetInput高度
$height-datepicker_preset_panel_inset_input: $height-datepicker_month_max + $spacing-datepicker_month-padding + $width-datepicker_nav + $spacing-datepicker_navigation_insetInput-paddingY * 2 + $height-datepicker_inset_input; // inset_input下，非month面板渲染最大高度，默认361px


// insetinput compact
$height-datepicker_inset_input_compact: 28px + $spacing-datepicker_insetInput_wrapper_compact-paddingY; // compact，insetInput高度, 默认36px
$height-datepicker_preset_panel_inset_input_compact: $height-datepicker_month_max_compact + $width-datepicker_nav_compact + $spacing-datepicker_insetInput_wrapper_compact-paddingY * 2 + $height-datepicker_inset_input_compact; // inset_input下，非month面板渲染最大高度，默认296px
