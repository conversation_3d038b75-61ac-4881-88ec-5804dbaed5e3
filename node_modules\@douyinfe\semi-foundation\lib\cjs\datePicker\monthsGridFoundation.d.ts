import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { strings } from './constants';
import { WeekStartNumber } from './_utils/getMonthTable';
import { DateInputFoundationProps, PresetPosition, ValueType } from './foundation';
import { MonthDayInfo } from './monthFoundation';
import { ArrayElement } from '../utils/type';
import { YearAndMonthFoundationProps } from './yearAndMonthFoundation';
type Type = ArrayElement<typeof strings.TYPE_SET>;
interface MonthsGridElementProps {
    navPrev?: any;
    navNext?: any;
    renderDate?: () => any;
    renderFullDate?: () => any;
}
export type PanelType = 'left' | 'right';
export type YearMonthChangeType = 'prevMonth' | 'nextMonth' | 'prevYear' | 'nextYear';
export interface MonthsGridFoundationProps extends MonthsGridElementProps, Pick<YearAndMonthFoundationProps, 'startYear' | 'endYear'> {
    type?: Type;
    /** may be null if selection is not complete when type is dateRange or dateTimeRange */
    defaultValue?: (Date | null)[];
    defaultPickerValue?: ValueType;
    multiple?: boolean;
    max?: number;
    splitPanels?: boolean;
    weekStartsOn?: WeekStartNumber;
    disabledDate?: (date: Date, options?: {
        rangeStart: string;
        rangeEnd: string;
    }) => boolean;
    disabledTime?: (date: Date | Date[], panelType: PanelType) => void;
    disabledTimePicker?: boolean;
    hideDisabledOptions?: boolean;
    onMaxSelect?: (v?: any) => void;
    timePickerOpts?: any;
    isControlledComponent?: boolean;
    rangeStart?: string;
    rangeInputFocus?: boolean | string;
    locale?: any;
    localeCode?: string;
    format?: string;
    startDateOffset?: () => void;
    endDateOffset?: () => void;
    autoSwitchDate?: boolean;
    density?: string;
    dateFnsLocale?: any;
    timeZone?: string | number;
    syncSwitchMonth?: boolean;
    onChange?: (value: [Date] | [Date, Date], options?: {
        closePanel?: boolean;
        needCheckFocusRecord?: boolean;
    }) => void;
    onPanelChange?: (date: Date | Date[], dateString: string | string[]) => void;
    setRangeInputFocus?: (rangeInputFocus: 'rangeStart' | 'rangeEnd') => void;
    isAnotherPanelHasOpened?: (currentRangeInput: 'rangeStart' | 'rangeEnd') => boolean;
    focusRecordsRef?: any;
    triggerRender?: (props: Record<string, any>) => any;
    insetInput: DateInputFoundationProps['insetInput'];
    presetPosition?: PresetPosition;
    renderQuickControls?: any;
    renderDateInput?: any;
}
export interface MonthInfo {
    /** The date displayed in the current date panel, update when switching year and month */
    pickerDate: Date;
    /**
     * Default date or selected date (when selected)
     */
    showDate: Date;
    isTimePickerOpen: boolean;
    isYearPickerOpen: boolean;
}
export interface MonthsGridFoundationState {
    selected: Set<string>;
    monthLeft: MonthInfo;
    monthRight: MonthInfo;
    maxWeekNum: number;
    hoverDay: string;
    rangeStart: string;
    rangeEnd: string;
    currentPanelHeight: number;
    offsetRangeStart: string;
    offsetRangeEnd: string;
    weeksRowNum?: number;
}
export interface MonthsGridDateAdapter {
    updateDaySelected: (selected: Set<string>) => void;
}
export interface MonthsGridRangeAdapter {
    setRangeStart: (rangeStart: string) => void;
    setRangeEnd: (rangeEnd: string) => void;
    setHoverDay: (hoverDay: string) => void;
    setWeeksHeight: (maxWeekNum: number) => void;
    setOffsetRangeStart: (offsetRangeStart: string) => void;
    setOffsetRangeEnd: (offsetRangeEnd: string) => void;
}
export interface MonthsGridAdapter extends DefaultAdapter<MonthsGridFoundationProps, MonthsGridFoundationState>, MonthsGridRangeAdapter, MonthsGridDateAdapter {
    updateMonthOnLeft: (v: MonthInfo) => void;
    updateMonthOnRight: (v: MonthInfo) => void;
    notifySelectedChange: MonthsGridFoundationProps['onChange'];
    notifyMaxLimit: MonthsGridFoundationProps['onMaxSelect'];
    notifyPanelChange: MonthsGridFoundationProps['onPanelChange'];
    setRangeInputFocus: MonthsGridFoundationProps['setRangeInputFocus'];
    isAnotherPanelHasOpened: MonthsGridFoundationProps['isAnotherPanelHasOpened'];
}
export default class MonthsGridFoundation extends BaseFoundation<MonthsGridAdapter> {
    newBiMonthPanelDate: [Date, Date];
    constructor(adapter: MonthsGridAdapter);
    init(): void;
    initDefaultPickerValue(): void;
    updateSelectedFromProps(values: (Date | null)[], refreshPicker?: boolean): void;
    calcDisabledTime(panelType: PanelType): any;
    _initDatePickerFromValue(values: Date[], refreshPicker?: boolean): void;
    _initDateRangePickerFromValue(values: (Date | null)[], withTime?: boolean): void;
    _initDateTimePickerFromValue(values: Date[]): void;
    _initDateTimeRangePickerFormValue(values: (Date | null)[]): void;
    destroy(): void;
    /**
     * sync change another panel month when change months from the else yam panel
     * call it when
     *  - current change panel targe date month is same with another panel date
     *
     * @example
     *  - panelType=right, target=new Date('2022-09-01') and left panel is in '2022-09' => call it, left panel minus one month to '2022-08'
     *  - panelType=left, target=new Date('2021-12-01') and right panel is in '2021-12' => call it, right panel add one month to '2021-01'
     */
    handleSyncChangeMonths(options: {
        panelType: PanelType;
        target: Date;
    }): void;
    /**
     * Get the target date based on the panel type and switch type
     */
    getTargetChangeDate(options: {
        panelType: PanelType;
        switchType: YearMonthChangeType;
    }): Date;
    /**
     * Change month by yam panel
     */
    toMonth(panelType: PanelType, target: Date): void;
    toYear(panelType: PanelType, target: Date): void;
    toYearMonth(panelType: PanelType, target: Date): void;
    isRangeType(type?: Type): boolean;
    handleSwitchMonthOrYear(switchType: YearMonthChangeType, panelType: PanelType): void;
    prevMonth(panelType: PanelType): void;
    nextMonth(panelType: PanelType): void;
    prevYear(panelType: PanelType): void;
    nextYear(panelType: PanelType): void;
    /**
     * Calculate the year and month difference
     */
    _getDiff(type: 'month' | 'year', target: Date, panelType: PanelType): number;
    _getPanelDetail(panelType: PanelType): any;
    /**
     * Format locale date
     * locale get from LocaleProvider
     * @param {Date} date
     * @param {String} token
     * @returns
     */
    localeFormat(date: Date, token: string): string;
    /**
     * 根据 type 处理 onChange 返回的参数
     *
     *  - 返回的日期需要把用户时间转换为设置的时区时间
     *      - 用户时间：用户计算机系统时间
     *      - 时区时间：通过 ConfigProvider 设置的 timeZone
     *  - 例子：用户设置时区为+9，计算机所在时区为+8区，然后用户选择了22:00
     *      - DatePicker 内部保存日期 state 为 +8 的 22:00 => a = new Date("2021-05-25 22:00:00")
     *      - 传出去时，需要把 +8 的 22:00 => +9 的 22:00 => b = zonedTimeToUtc(a, "+09:00");
     *
     * The parameters returned by onChange are processed according to type
     *
     *  -The returned date needs to convert the user time to the set time zone time
     *      -User time: user computer system time
     *      -Time zone: timeZone set by ConfigProvider
     *  -Example: The user sets the time zone to + 9, and the time zone where the computer is located is + 8, and then the user selects 22:00
     *      -DatePicker internal save date state is + 8 22:00 = > a = new Date ("2021-05-25 22:00:00")
     *      -When passing out, you need to put + 8's 22:00 = > + 9's 22:00 = > b = zonedTimeToUtc (a, "+ 09:00");
     *
     *  e.g.
     *  let a = new Date ("2021-05-25 22:00:00");
     *       = > Tue May 25 2021 22:00:00 GMT + 0800 (China Standard Time)
     *  let b = zonedTimeToUtc (a, "+ 09:00");
     *       = > Tue May 25 2021 21:00:00 GMT + 0800 (China Standard Time)
     *
     * @param {Date|Date[]} value
     */
    disposeCallbackArgs(value: Date | Date[]): {
        notifyValue: any;
        notifyDate: any;
    };
    handleYearOrMonthChange(type: YearMonthChangeType, panelType?: PanelType, step?: number, notSeparateInRange?: boolean): void;
    /**
     * You have chosen to switch the year and month in the future to directly update the Date without closing the date panel
     * @param {*} type
     * @param {*} targetDate
     */
    updateDateAfterChangeYM(type: YearMonthChangeType, targetDate: Date): void;
    _isMultiple(): boolean;
    _isRange(): void;
    handleDayClick(day: MonthDayInfo, panelType: PanelType): void;
    handleDateSelected(day: {
        fullDate: string;
        fullValidDate?: Date;
    }, panelType: PanelType): void;
    handleShowDateAndTime(panelType: PanelType, pickerDate: number | Date, showDate?: Date): void;
    /**
     * link date and time
     *
     * @param {Date|string} date
     * @param {Date|string} time
     * @returns {Date}
     */
    _mergeDateAndTime(date: Date | string, time: Date | string): Date;
    handleRangeSelected(day: MonthDayInfo): void;
    _isNeedSwap(rangeStart: Date | string, rangeEnd: Date | string): boolean;
    /**
     * Day may be empty, this is unhover state
     * @param {*} day
     */
    handleDayHover(day?: {
        fullDate: string;
    }, panelType?: PanelType): void;
    _autoAdjustMonth(monthLeft: MonthInfo, monthRight: MonthInfo): {
        monthLeft: MonthInfo;
        monthRight: MonthInfo;
    };
    getValidTimeFormat(): string;
    getValidDateFormat(): any;
    handleTimeChange(newTime: {
        timeStampValue: number;
    }, panelType: PanelType): void;
    /**
     * Update the time part in the range
     * @param {string} panelType
     * @param {Date} timeDate
     */
    _updateTimeInDateRange(panelType: PanelType, timeDate: Date): void;
    _updatePanelDetail(panelType: PanelType, kvs: {
        showDate?: number | Date;
        pickerDate?: number | Date;
        isTimePickerOpen?: boolean;
        isYearPickerOpen?: boolean;
    }): void;
    showYearPicker(panelType: PanelType): void;
    showTimePicker(panelType: PanelType, opt?: boolean): void;
    showDatePanel(panelType: PanelType): void;
    /**
     * Get year and month panel open type
     *
     * It is useful info to set minHeight of weeks.
     *  - When yam open type is 'left' or 'right', weeks minHeight should be set
     *    If the minHeight is not set, the change of the number of weeks will cause the scrollList to be unstable
     */
    getYAMOpenType(): "left" | "right" | "none" | "both";
}
export {};
