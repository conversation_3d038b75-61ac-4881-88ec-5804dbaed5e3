import React, { Component } from 'react';
import PropTypes from 'prop-types';
import noop from './utils/noop';
import Animation from './Animation';
export default class KeyFrames extends Component {
  constructor() {
    var _this;
    let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    super(props);
    _this = this;
    this.onFrame = function () {
      let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      const currentStyle = Object.assign({}, props);
      _this.props.onFrame(currentStyle);
      _this.setState({
        currentStyle
      });
    };
    this.next = () => {
      let {
        frameIndex
      } = this.state;
      const {
        frames,
        loop
      } = this.props;
      frameIndex++;
      if (frameIndex < frames.length - 1) {
        this.setState({
          frameIndex
        });
      } else {
        frameIndex = 0;
        this.props.onRest(this.state.currentStyle);
        if (loop) {
          this.setState({
            frameIndex
          });
        }
      }
      this.props.onKeyRest(this.state.currentStyle);
    };
    this.forwardInstance = instance => {
      this.instance = instance;
      if (typeof this.props.forwardInstance === 'function') {
        this.props.forwardInstance(this.instance);
      }
    };
    this.state = {
      currentStyle: {},
      frameIndex: 0
    };
  }
  componentDidMount() {
    // this.props.forwardInstance(this.instance);
  }
  componentWillUnmount() {
    this.instance && this.instance.destroy();
  }
  render() {
    const {
      children,
      frames
    } = this.props;
    const {
      frameIndex,
      currentStyle
    } = this.state;
    const from = frames[frameIndex];
    const to = frames[frameIndex + 1];
    return /*#__PURE__*/React.createElement(Animation, Object.assign({}, this.props, {
      forwardInstance: this.forwardInstance,
      from: from,
      to: to,
      onFrame: this.onFrame,
      onRest: this.next
    }), typeof children === 'function' ? children(currentStyle) : children);
  }
}
KeyFrames.propTypes = {
  frames: PropTypes.array,
  loop: PropTypes.bool,
  onFrame: PropTypes.func,
  onKeyRest: PropTypes.func,
  onRest: PropTypes.func
};
KeyFrames.defaultProps = {
  frames: [],
  loop: false,
  onKeyRest: noop,
  onRest: noop,
  onFrame: noop
};