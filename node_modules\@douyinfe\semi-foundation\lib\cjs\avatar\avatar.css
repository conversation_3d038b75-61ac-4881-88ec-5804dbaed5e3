/* shadow */
/* sizing */
/* spacing */
.semi-avatar {
  position: relative;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
}
.semi-avatar:focus-visible {
  outline: 2px solid var(--semi-color-primary-light-active);
}
.semi-avatar-focus {
  outline: 2px solid var(--semi-color-primary-light-active);
}
.semi-avatar-no-focus-visible:focus-visible {
  outline: none;
}
.semi-avatar .semi-avatar-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 20px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 600;
}
.semi-avatar-content {
  user-select: none;
}
.semi-avatar-extra-extra-small {
  width: 20px;
  height: 20px;
  border-radius: 3px;
}
.semi-avatar-extra-extra-small .semi-avatar-content {
  transform-origin: center;
  transform: scale(0.8);
}
.semi-avatar-extra-extra-small .semi-avatar-label {
  font-size: 10px;
  line-height: 15px;
}
.semi-avatar-extra-small {
  width: 24px;
  height: 24px;
  border-radius: 3px;
}
.semi-avatar-extra-small .semi-avatar-content {
  transform-origin: center;
  transform: scale(0.8);
}
.semi-avatar-extra-small .semi-avatar-label {
  font-size: 10px;
  line-height: 15px;
}
.semi-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 3px;
}
.semi-avatar-small .semi-avatar-label {
  font-size: 12px;
  line-height: 16px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.semi-avatar-default {
  width: 40px;
  height: 40px;
  border-radius: 3px;
}
.semi-avatar-default .semi-avatar-label {
  font-size: 18px;
  line-height: 24px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.semi-avatar-medium {
  width: 48px;
  height: 48px;
  border-radius: 3px;
}
.semi-avatar-medium .semi-avatar-label {
  font-size: 20px;
  line-height: 28px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.semi-avatar-large {
  width: 72px;
  height: 72px;
  border-radius: 6px;
}
.semi-avatar-large .semi-avatar-label {
  font-size: 32px;
  line-height: 44px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.semi-avatar-extra-large {
  width: 128px;
  height: 128px;
  border-radius: 12px;
}
.semi-avatar-extra-large .semi-avatar-label {
  font-size: 64px;
  line-height: 77px;
}
.semi-avatar-circle {
  border-radius: var(--semi-border-radius-circle);
}
.semi-avatar-image {
  background-color: transparent;
}
.semi-avatar > img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.semi-avatar-hover {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.semi-avatar:hover {
  cursor: pointer;
}

.semi-avatar-wrapper {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: fit-content;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg {
  position: absolute;
  display: flex;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-small {
  width: 32px;
  height: 32px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-default {
  width: 40px;
  height: 40px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-medium {
  width: 48px;
  height: 48px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-large {
  width: 72px;
  height: 72px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-extra-large {
  width: 128px;
  height: 128px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg {
  position: absolute;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg-small {
  top: -28px;
  scale: 0.4;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg-default {
  top: -32px;
  scale: 0.7;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg-medium {
  top: -30px;
  scale: 0.8;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg-large {
  top: -30px;
  scale: 1.1;
}
.semi-avatar-wrapper .semi-avatar-top_slot-bg-svg-extra-large {
  top: -32px;
  scale: 1.4;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper {
  position: absolute;
  display: flex;
  justify-content: center;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot {
  color: var(--semi-color-bg-0);
  font-weight: 600;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content {
  user-select: none;
  position: relative;
  line-height: normal;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content-small {
  font-size: 5px;
  margin-top: 0px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content-default {
  font-size: 6px;
  margin-top: -2px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content-medium {
  font-size: 8px;
  margin-top: 0px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content-large {
  font-size: 14px;
  margin-top: 0px;
}
.semi-avatar-wrapper .semi-avatar-top_slot-wrapper .semi-avatar-top_slot-content-extra-large {
  font-size: 16px;
  margin-top: 0px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot {
  color: var(--semi-color-bg-0);
  position: absolute;
  cursor: pointer;
  bottom: 3.5px;
  transform: translateY(50%);
  user-select: none;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--semi-color-primary);
  border-radius: var(--semi-border-radius-circle);
  line-height: normal;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-extra-small {
  width: 12px;
  height: 12px;
  font-size: 5px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-small {
  width: 12px;
  height: 12px;
  font-size: 5px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-default {
  width: 16px;
  height: 16px;
  font-size: 12px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-medium {
  width: 18px;
  height: 18px;
  font-size: 12px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-large {
  width: 28px;
  height: 28px;
  font-size: 12px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_circle-extra-large {
  width: 28px;
  height: 28px;
  font-size: 14px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--semi-color-primary);
  border-radius: 4px;
  padding: 1px 4px;
  font-weight: 600;
  border-style: solid;
  border-color: var(--semi-color-bg-0);
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-extra_small {
  font-size: 5px;
  border-width: 2px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-small {
  font-size: 5px;
  border-width: 2px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-default {
  font-size: 12px;
  border-width: 2px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-medium {
  font-size: 12px;
  border-width: 2px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-large {
  font-size: 12px;
  border-width: 2px;
}
.semi-avatar-wrapper .semi-avatar-bottom_slot-shape_square-extra-large {
  font-size: 14px;
  border-width: 2px;
}

.semi-avatar-group {
  display: inline-block;
}
.semi-avatar-group .semi-avatar {
  box-sizing: border-box;
}
.semi-avatar-group .semi-avatar:first-child {
  margin-left: 0;
}
.semi-avatar-group .semi-avatar-extra-large {
  border: 3px var(--semi-color-bg-1) solid;
  margin-left: -32px;
}
.semi-avatar-group .semi-avatar-large {
  border: 3px var(--semi-color-bg-1) solid;
  margin-left: -18px;
}
.semi-avatar-group .semi-avatar-medium {
  border: 2px var(--semi-color-bg-1) solid;
  margin-left: -12px;
}
.semi-avatar-group .semi-avatar-default {
  border: 2px var(--semi-color-bg-1) solid;
  margin-left: -12px;
}
.semi-avatar-group .semi-avatar-small {
  border: 2px var(--semi-color-bg-1) solid;
  margin-left: -12px;
}
.semi-avatar-group .semi-avatar-extra-small {
  border: 1px var(--semi-color-bg-1) solid;
  margin-left: -10px;
}
.semi-avatar-group .semi-avatar-extra-extra-small {
  border: 1px var(--semi-color-bg-1) solid;
  margin-left: -4px;
}
.semi-avatar-group .semi-avatar-item-start-0 {
  z-index: 100;
}
.semi-avatar-group .semi-avatar-item-end-0 {
  z-index: 80;
}
.semi-avatar-group .semi-avatar-item-start-1 {
  z-index: 99;
}
.semi-avatar-group .semi-avatar-item-end-1 {
  z-index: 81;
}
.semi-avatar-group .semi-avatar-item-start-2 {
  z-index: 98;
}
.semi-avatar-group .semi-avatar-item-end-2 {
  z-index: 82;
}
.semi-avatar-group .semi-avatar-item-start-3 {
  z-index: 97;
}
.semi-avatar-group .semi-avatar-item-end-3 {
  z-index: 83;
}
.semi-avatar-group .semi-avatar-item-start-4 {
  z-index: 96;
}
.semi-avatar-group .semi-avatar-item-end-4 {
  z-index: 84;
}
.semi-avatar-group .semi-avatar-item-start-5 {
  z-index: 95;
}
.semi-avatar-group .semi-avatar-item-end-5 {
  z-index: 85;
}
.semi-avatar-group .semi-avatar-item-start-6 {
  z-index: 94;
}
.semi-avatar-group .semi-avatar-item-end-6 {
  z-index: 86;
}
.semi-avatar-group .semi-avatar-item-start-7 {
  z-index: 93;
}
.semi-avatar-group .semi-avatar-item-end-7 {
  z-index: 87;
}
.semi-avatar-group .semi-avatar-item-start-8 {
  z-index: 92;
}
.semi-avatar-group .semi-avatar-item-end-8 {
  z-index: 88;
}
.semi-avatar-group .semi-avatar-item-start-9 {
  z-index: 91;
}
.semi-avatar-group .semi-avatar-item-end-9 {
  z-index: 89;
}
.semi-avatar-group .semi-avatar-item-start-10 {
  z-index: 90;
}
.semi-avatar-group .semi-avatar-item-end-10 {
  z-index: 90;
}
.semi-avatar-group .semi-avatar-item-start-11 {
  z-index: 89;
}
.semi-avatar-group .semi-avatar-item-end-11 {
  z-index: 91;
}
.semi-avatar-group .semi-avatar-item-start-12 {
  z-index: 88;
}
.semi-avatar-group .semi-avatar-item-end-12 {
  z-index: 92;
}
.semi-avatar-group .semi-avatar-item-start-13 {
  z-index: 87;
}
.semi-avatar-group .semi-avatar-item-end-13 {
  z-index: 93;
}
.semi-avatar-group .semi-avatar-item-start-14 {
  z-index: 86;
}
.semi-avatar-group .semi-avatar-item-end-14 {
  z-index: 94;
}
.semi-avatar-group .semi-avatar-item-start-15 {
  z-index: 85;
}
.semi-avatar-group .semi-avatar-item-end-15 {
  z-index: 95;
}
.semi-avatar-group .semi-avatar-item-start-16 {
  z-index: 84;
}
.semi-avatar-group .semi-avatar-item-end-16 {
  z-index: 96;
}
.semi-avatar-group .semi-avatar-item-start-17 {
  z-index: 83;
}
.semi-avatar-group .semi-avatar-item-end-17 {
  z-index: 97;
}
.semi-avatar-group .semi-avatar-item-start-18 {
  z-index: 82;
}
.semi-avatar-group .semi-avatar-item-end-18 {
  z-index: 98;
}
.semi-avatar-group .semi-avatar-item-start-19 {
  z-index: 81;
}
.semi-avatar-group .semi-avatar-item-end-19 {
  z-index: 99;
}
.semi-avatar-group .semi-avatar-item-start-20 {
  z-index: 80;
}
.semi-avatar-group .semi-avatar-item-end-20 {
  z-index: 100;
}
.semi-avatar-group .semi-avatar-item-more {
  background-color: rgba(var(--semi-grey-5), 1);
}

.semi-avatar-amber {
  background-color: rgba(var(--semi-amber-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-blue {
  background-color: rgba(var(--semi-blue-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-cyan {
  background-color: rgba(var(--semi-cyan-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-green {
  background-color: rgba(var(--semi-green-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-grey {
  background-color: rgba(var(--semi-grey-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-indigo {
  background-color: rgba(var(--semi-indigo-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-light-blue {
  background-color: rgba(var(--semi-light-blue-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-light-green {
  background-color: rgba(var(--semi-light-green-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-lime {
  background-color: rgba(var(--semi-lime-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-orange {
  background-color: rgba(var(--semi-orange-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-pink {
  background-color: rgba(var(--semi-pink-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-purple {
  background-color: rgba(var(--semi-purple-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-red {
  background-color: rgba(var(--semi-red-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-teal {
  background-color: rgba(var(--semi-teal-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-violet {
  background-color: rgba(var(--semi-violet-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-yellow {
  background-color: rgba(var(--semi-yellow-3), 1);
  color: rgba(var(--semi-white), 1);
}

.semi-avatar-additionalBorder {
  border-style: solid;
  border-color: var(--semi-color-primary);
  display: inline-block;
  box-sizing: border-box;
  position: absolute;
  border-width: 1.5px;
  top: -3.5px;
  left: -3.5px;
}
.semi-avatar-additionalBorder-extra-extra-small {
  width: 27px;
  height: 27px;
}
.semi-avatar-additionalBorder-extra-small {
  width: 31px;
  height: 31px;
}
.semi-avatar-additionalBorder-small {
  width: 39px;
  height: 39px;
}
.semi-avatar-additionalBorder-default {
  width: 47px;
  height: 47px;
}
.semi-avatar-additionalBorder-medium {
  width: 55px;
  height: 55px;
}
.semi-avatar-additionalBorder-large {
  width: 79px;
  height: 79px;
}
.semi-avatar-additionalBorder-extra-large {
  width: 135px;
  height: 135px;
}

.semi-avatar-square.semi-avatar-additionalBorder-extra_extra_small {
  border-radius: 3px;
}

.semi-avatar-square.semi-avatar-additionalBorder-extra_small {
  border-radius: 3px;
}

.semi-avatar-square.semi-avatar-additionalBorder-small {
  border-radius: 3px;
}

.semi-avatar-square.semi-avatar-additionalBorder-default {
  border-radius: 3px;
}

.semi-avatar-square.semi-avatar-additionalBorder-medium {
  border-radius: 3px;
}

.semi-avatar-square.semi-avatar-additionalBorder-large {
  border-radius: 6px;
}

.semi-avatar-additionalBorder-circle {
  border-radius: var(--semi-border-radius-circle);
}

.semi-avatar-additionalBorder-animated {
  animation: 800ms linear infinite semi-avatar-additionalBorder;
}

.semi-avatar-animated {
  animation: 1000ms linear infinite semi-avatar-content;
}

@keyframes semi-avatar-additionalBorder {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  to {
    border-width: 0;
    opacity: 0;
    transform: scale(1.15);
  }
}
@keyframes semi-avatar-content {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  to {
    transform: scale(1);
  }
}
.semi-rtl .semi-avatar,
.semi-portal-rtl .semi-avatar {
  direction: rtl;
}
.semi-rtl .semi-avatar-extra-extra-small .semi-avatar-content,
.semi-portal-rtl .semi-avatar-extra-extra-small .semi-avatar-content {
  transform: scale(0.8);
}
.semi-rtl .semi-avatar-extra-small .semi-avatar-content,
.semi-portal-rtl .semi-avatar-extra-small .semi-avatar-content {
  transform: scale(0.8);
}
.semi-rtl .semi-avatar-hover,
.semi-portal-rtl .semi-avatar-hover {
  left: auto;
  right: 0;
}
.semi-rtl .semi-avatar-group,
.semi-portal-rtl .semi-avatar-group {
  direction: rtl;
}
.semi-rtl .semi-avatar-group .semi-avatar:first-child,
.semi-portal-rtl .semi-avatar-group .semi-avatar:first-child {
  margin-left: auto;
  margin-right: 0;
}
.semi-rtl .semi-avatar-group .semi-avatar-extra-large,
.semi-portal-rtl .semi-avatar-group .semi-avatar-extra-large {
  margin-left: auto;
  margin-right: -32px;
}
.semi-rtl .semi-avatar-group .semi-avatar-large,
.semi-portal-rtl .semi-avatar-group .semi-avatar-large {
  margin-left: auto;
  margin-right: -18px;
}
.semi-rtl .semi-avatar-group .semi-avatar-medium,
.semi-rtl .semi-avatar-group .semi-avatar-small,
.semi-portal-rtl .semi-avatar-group .semi-avatar-medium,
.semi-portal-rtl .semi-avatar-group .semi-avatar-small {
  margin-left: auto;
  margin-right: -12px;
}
.semi-rtl .semi-avatar-group .semi-avatar-extra-small,
.semi-portal-rtl .semi-avatar-group .semi-avatar-extra-small {
  margin-left: auto;
  margin-right: -10px;
}
.semi-rtl .semi-avatar-group .semi-avatar-extra-extra-small,
.semi-portal-rtl .semi-avatar-group .semi-avatar-extra-extra-small {
  margin-left: auto;
  margin-right: -4px;
}