import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export type BackTopClickEvent = any;
export type Target = any;
export interface BackTopAdapter extends DefaultAdapter {
    updateVisible: (visible: boolean) => void;
    notifyClick: (e: BackTopClickEvent) => void;
    targetIsWindow: (target: any) => boolean;
    isWindowUndefined: () => boolean;
    targetScrollToTop: (targetNode: any, scrollTop: number) => void;
}
export default class BackTopFoundation extends BaseFoundation<BackTopAdapter> {
    animation: any;
    constructor(adapter: BackTopAdapter);
    init(): void;
    destroy(): void;
    getScroll(target: Target): any;
    scrollTo: (targetNode: Target, from: number, to: number) => void;
    setScrollTop(to: number): void;
    handleScroll: () => void;
    onClick(e: BackTopClickEvent): void;
}
