// Color
$color-anchor_slide_default-bg-default: var(--semi-color-border); // 滑轨背景颜色
$color-anchor_slide_primary-bg-active: var(--semi-color-primary); // 选中颜色 - Primary
$color-anchor_slide_tertiary-bg-active: var(--semi-color-tertiary); // 选中颜色 - Tietrary
$color-anchor_slide_muted-bg-active: var(--semi-color-white); // 选中颜色 - Muted

$color-anchor_title-text-default: var(--semi-color-text-2); // 文字颜色 - 未选中
$color-anchor_title-text-hover: var(--semi-color-tertiary-hover); // 文字颜色 - 未选中悬浮态
$color-anchor_title_active-text-hover: $color-anchor_title-text-hover;  // 文字颜色 - 选中悬浮态
$color-anchor_title-text-active: var(--semi-color-text-0); // 文字颜色 - 选中
$color-anchor_title-text-disabled: var(--semi-color-disabled-text); // 文字颜色 - 禁用

$color-anchor_title-bg-default: transparent; // 背景色
$color-anchor_title-bg-active: transparent; // 背景色 - 选中

$color-anchor_title-outline-focus: var(--semi-color-primary-light-active); // 轮廓 - 聚焦

// Spacing
$spacing-anchor_slide-left: $spacing-none; // 滑轨左侧位置
$spacing-anchor_slide-top: $spacing-none; // 滑轨顶部位置
$spacing-anchor_slide_default-Y: $spacing-extra-tight; // 选项间距 - 默认
$spacing-anchor_slide_small-Y: $spacing-extra-tight; // 选项间距 - 紧凑
$spacing-anchor_link-paddingLeft: $spacing-tight; // 选项文字左边距
$spacing-anchor_link_title-paddingTop: $spacing-extra-tight; // 选项文字顶部内边距
$spacing-anchor_link_title-paddingBottom: $spacing-extra-tight; // 选项文字底部内边距

// Size
$width-anchor_slide_default: 2px; // 滑轨宽度
$height-anchor_slide_default: 20px; // 选项高度 - 默认
$height-anchor_slide_small: 16px; // 选项高度 - 小尺寸

// Radius
$radius-anchor_slide: 1px; // 滑轨圆角

// Witdh
$width-anchor-outline: 2px; // anchor轮廓宽度
$width-anchor-outlineOffset: -2px; // anchor轮廓 outline-offset 宽度
$width-anchor-outline_border_radius: 3px; // anchor轮廓圆角
