export declare function isValid(val: any): boolean;
export declare function normalizedArr(val: any): any[];
/**
 * @returns whether option includes sugInput.
 * When filterTreeNode is a function,returns the result of filterTreeNode which called with (sugInput, target, option).
 */
export declare function filter(sugInput: string, option: any, filterTreeNode: any, filteredPath?: string[]): any;
export declare function getKeysByValuePath(valuePath: (string | number)[][] | (string | number)[]): string[];
export declare function getKeyByValuePath(valuePath: (string | number)[]): string;
export declare function getValuePathByKey(key: string): string[];
export declare function getKeyByPos(pos: string, treeData: any): string;
export declare function convertDataToEntities(dataNodes: any): any;
export declare function calcMergeType(autoMergeValue: boolean, leafOnly: boolean): string;
