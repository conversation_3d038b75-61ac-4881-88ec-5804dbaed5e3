@import './variables.scss';

$module: #{$prefix}-codeHighlight;

.#{$module}{
  &-defaultTheme{
    /**
     * prism.js default theme for JavaScript, CSS and HTML
     * Based on dabblet (http://dabblet.com)
     * <AUTHOR> Verou
     */

    pre[class*="language-"],
    code[class*="language-"]{
      color: var(--semi-color-text-0);
      font-size: 13px;
      text-shadow: none;
      // font-family: 'Inconsolata', Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      direction: ltr;
      text-align: left;
      white-space: pre;
      word-spacing: normal;
      word-break: normal;
      line-height: 1.5;
      -moz-tab-size: 4;
      -o-tab-size: 4;
      tab-size: 4;

      -moz-hyphens: none;
      hyphens: none;
    }

    pre[class*="language-"]::selection,
    code[class*="language-"]::selection,
    pre[class*="language-"]::mozselection,
    code[class*="language-"]::mozselection {
      text-shadow: none;
      background: #b3d4fc;
    }


    pre[class*="language-"]{
      padding: 1em;
      margin: .5em 0;
      overflow: auto;
      background: var(--semi-color-tertiary-light-default);
    }

    :not(pre)>code[class*="language-"] {
      // padding: .1em .3em;
      display: block;
      border-radius: .3em;
      color: #895fe2;
      background: #f9f7f9;
    }

    pre{
      .namespace {
        opacity: .7;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: #6b7075;
      }

      .token.punctuation {
        color: rgba(var(--semi-grey-8), 1),
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol,
      .token.deleted {
        color: rgba(var(--semi-purple-6), 1);
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin,
      .token.inserted {
        color: rgba(var(--semi-green-6), 1);
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string {
        color: rgba(var(--semi-grey-8), 1),
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: rgba(var(--semi-purple-6), 1);
      }

      .token.function {
        color: rgba(var(--semi-violet-6), 1);
      }

      .token.regex,
      .token.important,
      .token.variable {
        color: #d0955f;
      }

      .token.important,
      .token.bold {
        font-weight: bold;
      }

      .token.italic {
        font-style: italic;
      }

      .token.entity {
        cursor: help;
      }

    }

    pre[data-line] {
      position: relative;
    }

    pre[class*="language-"]>code[class*="language-"] {
      position: relative;
      z-index: 1;
    }

    .line-highlight {
      position: absolute;
      left: 0;
      right: 0;
      padding: inherit 0;
      margin-top: 1em;
      background: #ebf4ff;
      box-shadow: inset 5px 0 0 #0064d2;
      z-index: 0;
      pointer-events: none;
      line-height: inherit;
      white-space: pre;
    }
  }

  //---- line number ----


  pre[class*="language-"].line-numbers {
    position: relative;
    padding-left: 3.8em;
    counter-reset: linenumber;
  }

  pre[class*="language-"].line-numbers > code {
    position: relative;
    white-space: inherit;
  }

  .line-numbers .line-numbers-rows {
    position: absolute;
    pointer-events: none;
    top: 0;
    font-size: 100%;
    left: -3.8em;
    width: 3em; /* works for line-numbers below 1000 lines */
    letter-spacing: -1px;
    border-right: 1px solid #999;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

  }

  .line-numbers-rows > span {
    display: block;
    counter-increment: linenumber;
  }

  .line-numbers-rows > span:before {
    content: counter(linenumber);
    color: #999;
    display: block;
    padding-right: 0.8em;
    text-align: right;
  }

}



