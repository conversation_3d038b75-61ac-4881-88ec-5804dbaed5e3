"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _foundation = _interopRequireDefault(require("../base/foundation"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var __awaiter = void 0 && (void 0).__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
class PinCodeFoundation extends _foundation.default {
  constructor(adapter) {
    var _this;
    super(Object.assign({}, adapter));
    _this = this;
    this.handleCurrentActiveIndexChange = (index, state) => {
      if (state === "focus") {
        this._adapter.onCurrentActiveIndexChange(index);
      }
    };
    this.completeSingleInput = (i, singleInputValue) => __awaiter(this, void 0, void 0, function* () {
      var _a;
      const isControlledComponent = this._isInProps("value");
      yield this._adapter.onCurrentActiveIndexChange(i + 1);
      const valueList = [...this.getState("valueList")];
      valueList[i] = singleInputValue;
      this._adapter.notifyValueChange(valueList);
      if (!isControlledComponent) {
        yield this.updateValueList(valueList);
      }
      const count = this.getProp('count');
      if (i + 1 > count - 1) {
        this._adapter.changeSpecificInputFocusState(i, "blur");
        (_a = this.getProp("onComplete")) === null || _a === void 0 ? void 0 : _a(valueList.join(""));
      } else {
        this._adapter.changeSpecificInputFocusState(i + 1, "focus");
      }
    });
    this.validateValue = function () {
      let value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "";
      const format = _this.getProp("format");
      let validateFunction = value => true;
      if (typeof format === "string") {
        if (format === "number") {
          validateFunction = value => value.length === 0 || PinCodeFoundation.numberReg.test(value);
        } else if (format === "mixed") {
          validateFunction = value => value.length === 0 || PinCodeFoundation.mixedReg.test(value);
        }
      } else if (format instanceof RegExp) {
        validateFunction = value => format.test(value);
      } else if (typeof format === "function") {
        validateFunction = format;
      }
      return validateFunction(value);
    };
    this.updateValueList = newValueList => __awaiter(this, void 0, void 0, function* () {
      this._adapter.updateValueList(newValueList);
    });
    this.handlePaste = (e, startInputIndex) => __awaiter(this, void 0, void 0, function* () {
      const textWillPaste = e.clipboardData.getData("text");
      const count = this.getProp("count");
      for (let i = startInputIndex, charIndex = 0; i < count && charIndex < textWillPaste.length; i++, charIndex++) {
        const currentChar = textWillPaste[charIndex];
        if (this.validateValue(currentChar)) {
          yield this.completeSingleInput(i, currentChar);
        } else {
          break;
        }
      }
      e.preventDefault();
    });
    this.handleKeyDownOnSingleInput = (e, index) => {
      const valueList = [...this.getState("valueList")];
      if (e.key === "Backspace") {
        valueList[index] = "";
        this.updateValueList(valueList);
        this._adapter.notifyValueChange(valueList);
        this._adapter.changeSpecificInputFocusState(Math.max(0, index - 1), "focus");
        e.preventDefault();
      } else if (e.key === "Delete") {
        valueList[index] = "";
        this.updateValueList(valueList);
        this._adapter.notifyValueChange(valueList);
        this._adapter.changeSpecificInputFocusState(Math.min(valueList.length - 1, index + 1), "focus");
        e.preventDefault();
      } else if (e.key === "ArrowLeft") {
        this._adapter.changeSpecificInputFocusState(Math.max(0, index - 1), "focus");
        e.preventDefault();
      } else if (e.key === "ArrowRight") {
        this._adapter.changeSpecificInputFocusState(Math.min(valueList.length - 1, index + 1), "focus");
        e.preventDefault();
      }
    };
  }
}
PinCodeFoundation.numberReg = /^\d*$/;
PinCodeFoundation.mixedReg = /^[0-9a-zA-Z]$/;
var _default = exports.default = PinCodeFoundation;