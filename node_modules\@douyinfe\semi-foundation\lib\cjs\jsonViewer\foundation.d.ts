import { JsonViewer, JsonViewerOptions, CustomRenderRule } from '@douyinfe/semi-json-viewer-core';
import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export type { JsonViewerOptions, CustomRenderRule };
export interface JsonViewerAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    getEditorRef: () => HTMLElement;
    getSearchRef: () => HTMLInputElement;
    notifyChange: (value: string) => void;
    notifyHover: (value: string, el: HTMLElement) => HTMLElement | undefined;
    setSearchOptions: (key: string) => void;
    showSearchBar: () => void;
    notifyCustomRender: (customRenderMap: Map<HTMLElement, any>) => void;
}
declare class JsonViewerFoundation extends BaseFoundation<JsonViewerAdapter> {
    constructor(adapter: JsonViewerAdapter);
    jsonViewer: <PERSON>sonViewer | null;
    init(): void;
    search(searchText: string, caseSensitive?: boolean, wholeWord?: boolean, regex?: boolean): void;
    prevSearch(step?: number): void;
    nextSearch(step?: number): void;
    replace(replaceText: string): void;
    replaceAll(replaceText: string): void;
    setSearchOptions(key: string): void;
    showSearchBar(): void;
    getSearchResults(): import("@douyinfe/semi-json-viewer-core/src/common/model").FindMatch[];
}
export default JsonViewerFoundation;
