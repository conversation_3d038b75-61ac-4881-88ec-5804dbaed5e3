import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export type TagInputChangeEvent = any;
export type TagInputCursorEvent = any;
export type TagInputKeyboardEvent = any;
export type TagInputMouseEvent = any;
export interface OnSortEndProps {
    oldIndex: number;
    newIndex: number;
}
export interface TagInputAdapter extends DefaultAdapter {
    setInputValue: (inputValue: string) => void;
    setTagsArray: (tagsArray: string[]) => void;
    setFocusing: (focusing: boolean) => void;
    toggleFocusing(focused: boolean): void;
    setHovering: (hovering: boolean) => void;
    setActive: (active: boolean) => void;
    setEntering: (entering: boolean) => void;
    getClickOutsideHandler: () => any;
    registerClickOutsideHandler: (cb: any) => void;
    unregisterClickOutsideHandler: () => void;
    notifyBlur: (e: TagInputCursorEvent) => void;
    notifyFocus: (e: TagInputCursorEvent) => void;
    notifyInputChange: (v: string, e: TagInputChangeEvent) => void;
    notifyTagChange: (v: string[]) => void;
    notifyTagAdd: (v: string[]) => void;
    notifyTagRemove: (v: string, idx: number) => void;
    notifyKeyDown: (e: TagInputMouseEvent) => void;
}
declare class TagInputFoundation extends BaseFoundation<TagInputAdapter> {
    constructor(adapter: TagInputAdapter);
    /**
     * handler of input change
     */
    handleInputChange: (e: TagInputChangeEvent) => void;
    handleInputCompositionStart: (e: any) => void;
    handleInputCompositionEnd: (e: any) => void;
    /**
     * check whether the input change is legal
     */
    _checkInputChangeValid: (value: string) => boolean;
    /**
     * Input event handler when onKeyDown is triggered
     */
    handleKeyDown: (e: TagInputKeyboardEvent) => void;
    _handleAddTags(e: TagInputChangeEvent): void;
    handleInputBlur(e: TagInputCursorEvent): void;
    handleInputFocus(e: TagInputCursorEvent): void;
    /**
     * A11y: simulate clear button click
     */
    handleClearEnterPress(e: TagInputKeyboardEvent): void;
    handleClearBtn(e: TagInputMouseEvent): void;
    handleTagClose(index: number): void;
    handleInputMouseEnter(): void;
    handleInputMouseLeave(): void;
    handleClick(e?: any): void;
    clickOutsideCallBack(): void;
    handleClickPrefixOrSuffix(e: any): void;
    handlePreventMouseDown(e: any): void;
    /**
     * handler of delete tag
     */
    _onRemove(newTagList: string[], removedTags: string, index: number): void;
    /**
     * handler of add tag
     */
    _onAdd(newTagList: string[], addTags: string[]): void;
    /**
     * handler of input change
     */
    _onInputChange(value: string, e: TagInputChangeEvent): void;
    handleSortEnd(callbackProps: OnSortEndProps): void;
}
export default TagInputFoundation;
