import { Locale } from 'date-fns';
import { EventObject, ParsedRangeEvent } from './foundation';
export declare const sortDate: (a: Date | string, b: Date | string) => 1 | -1;
export declare const checkWeekend: (val: Date) => boolean;
export declare const getCurrDate: () => Date;
export declare const round: (value: number) => number;
export declare const getPos: (value: Date | number) => number;
export declare const isAllDayEvent: (event: EventObject) => boolean;
/**
 *
 * @param {object} event
 * normalize event object:
 * if event object does not have start time, add start time = end time - 1h; if not same day, then startday of the end
 * if event object does not have end time, add end time = start time + 1h; if not same day, then endday of the start
 */
export declare const amendEvent: (event: EventObject) => EventObject;
/**
 *
 * @param {arr} events
 * find the max topInd and used as row height
 */
export declare const calcRowHeight: (events: ParsedRangeEvent[]) => number;
export interface DateObj {
    ind: number;
    date: Date;
    dayString: string;
    weekday: string;
    isToday: boolean;
    isWeekend: boolean;
    isSameMonth: boolean;
    month: string;
}
export type weekStartsOnEnum = 0 | 1 | 2 | 3 | 4 | 5 | 6;
export declare const calcRangeData: (value: Date, start: Date, rangeLen: number, mode: string, locale: Locale, weekStartsOn: weekStartsOnEnum) => DateObj[];
/**
 *
 * @param {Date} date
 * @param {Date} monthStart current month start date, using for month mode
 * @param {string} mode
 * @param {string} locale
 * @returns {object[]} { date: Date, dayString: string, ind: number, isToday: boolean, isWeekend: boolean, weekday: string }
 * create weekly object array
 */
export declare const calcWeekData: (value: Date, monthStart: Date | null, mode: string, locale: Locale, weekStartsOn: weekStartsOnEnum) => DateObj[];
/**
 *
 * @param {object} event
 * @param {boolean} allDay
 * @returns {object[]} { allDay: boolean, data: Date, start: Date, end: Date, children: ReactNode }
 * parsed a spanned all-day event into multiple dates
 */
export declare const parseAllDayEvent: (event: EventObject, allDay?: boolean, currDate?: Date) => any[];
/**
 *
 * @param {object} event
 * @returns {object[]} { allDay: boolean, data: Date, start: Date, end: Date, children: ReactNode }
 * parsed events
 */
export declare const parseEvent: (event: EventObject) => any[];
/**
 *
 * @param {arr} arr
 * @param  {key}
 * @param {function} func callback function
 * @returns {map}
 * convert events array to may, use datestring as key
 */
export declare const convertEventsArrToMap: (arr: EventObject[], key: 'start' | 'date', func: (val: Date) => Date, displayValue?: Date) => Map<any, any>;
/**
 * @returns {arr}
 * filter out event that is not in the date range
 */
export declare const filterEvents: (events: Map<string, EventObject[]>, start: Date, end: Date) => Map<string, EventObject[]>;
/**
 * @returns {arr}
 * filter out event that is not in the week range
 */
export declare const filterWeeklyEvents: (events: Map<string, EventObject[]>, weekStart: Date, weekStartsOn: weekStartsOnEnum) => Map<string, EventObject[]>;
/**
 * @returns {arr}
 * arrange and sort all day event for a range
 */
export declare const parseRangeAllDayEvent: (event: EventObject[], startDate: Date, rangeStart: Date, rangeEnd: Date, parsed: Array<Array<ParsedRangeEvent>>) => ParsedRangeEvent[][];
/**
 * @returns {arr}
 * arrange and sort weekly all day event
 */
export declare const parseWeeklyAllDayEvent: (event: EventObject[], startDate: Date, weekStart: Date, parsed: Array<Array<ParsedRangeEvent>>, weekStartsOn: weekStartsOnEnum) => ParsedRangeEvent[][];
export declare const collectDailyEvents: (events: ParsedRangeEvent[][]) => ParsedRangeEvent[][];
export declare const renderDailyEvent: (event: EventObject) => {
    startPos: number;
    endPos: number;
    children: any;
    allDay: boolean;
};
