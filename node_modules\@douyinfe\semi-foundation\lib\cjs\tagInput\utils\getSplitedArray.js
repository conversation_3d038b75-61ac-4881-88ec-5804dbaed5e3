"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _isNumber2 = _interopRequireDefault(require("lodash/isNumber"));
var _isArray2 = _interopRequireDefault(require("lodash/isArray"));
var _isString2 = _interopRequireDefault(require("lodash/isString"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
/**
 * Get the splited array.
 * We expect separators to be string | string[] | null, but users
 * are also allowed to pass in other types.
 */
const getSplitedArray = (originString, separators) => {
  let splitedValue = [];
  if ((0, _isString2.default)(separators) || (0, _isNumber2.default)(separators)) {
    splitedValue = originString.split(separators);
  } else if ((0, _isArray2.default)(separators)) {
    const tempChar = separators[0]; // temporary splitter
    splitedValue = originString;
    for (let i = 1; i < separators.length; i++) {
      splitedValue = splitedValue.split(separators[i]).join(tempChar);
    }
    splitedValue = splitedValue.split(tempChar);
  } else {
    splitedValue.push(originString);
  }
  return splitedValue;
};
var _default = exports.default = getSplitedArray;