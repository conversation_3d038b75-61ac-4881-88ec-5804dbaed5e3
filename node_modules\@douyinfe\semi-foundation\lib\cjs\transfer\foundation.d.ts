import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { BasicValue as BasicTreeValue } from '../tree/foundation';
export interface BasicDataItem {
    [x: string]: any;
    key: string | number;
    label?: any;
    value?: string | number;
    disabled?: boolean;
    style?: any;
    className?: string;
}
export type DataItemMap = Map<number | string, BasicDataItem>;
export interface OnSortEndProps {
    oldIndex: number;
    newIndex: number;
}
export interface BasicResolvedDataItem extends BasicDataItem {
    _parent?: {
        title: string;
    };
    _optionKey?: string | number;
}
export interface TransferAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    getSelected: () => DataItemMap;
    updateSelected: (selectedItems: DataItemMap) => void;
    notifyChange: (values: Array<number | string>, items: Array<BasicDataItem>) => void;
    notifySearch: (input: string) => void;
    notifySelect: (items: BasicDataItem) => void;
    notifyDeselect: (items: BasicDataItem) => void;
    updateInput: (input: string) => void;
    updateSearchResult: (searchResult: Set<number | string>) => void;
    searchTree: (keyword: string) => void;
}
export default class TransferFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<TransferAdapter<P, S>> {
    constructor(adapter: TransferAdapter<P, S>);
    _generateGroupedData(dataSource: any[]): any[];
    _generateTreeData(dataSource: any[]): any[];
    _generatePath(item: BasicResolvedDataItem): any;
    handleInputChange(inputVal: string, notify: boolean): void;
    handleAll(wantAllChecked: boolean): void;
    handleClear(): void;
    handleSelectOrRemove(item: BasicResolvedDataItem): void;
    handleSelect(values: BasicTreeValue): void;
    getValuesAndItemsFromMap(selectedItems: DataItemMap): {
        items: any[];
        values: any[];
    };
    _notifyChange(selectedItems: DataItemMap): void;
    handleSortEnd(callbackProps: OnSortEndProps): void;
}
