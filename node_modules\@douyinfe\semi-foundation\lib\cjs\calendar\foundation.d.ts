import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { Locale } from 'date-fns';
import { DateObj } from './eventUtil';
import type { weekStartsOnEnum } from './eventUtil';
export { weekStartsOnEnum };
export interface EventObject {
    [x: string]: any;
    key: string;
    allDay?: boolean;
    start?: Date;
    end?: Date;
    children?: any;
}
export interface ParsedEventsWithArray {
    day: Array<any>;
    allDay: Array<any>;
}
export interface ParsedEvents {
    day?: Map<string, Array<EventObject>>;
    allDay?: Map<string, Array<EventObject>>;
}
export interface ParsedRangeEvent extends EventObject {
    leftPos?: number;
    width?: number;
    topInd?: number;
}
export interface MonthlyEvent {
    day: ParsedRangeEvent[][];
    display: ParsedRangeEvent[];
}
export interface RangeData {
    month: string;
    week: any[];
}
export interface WeeklyData {
    month: string;
    week: DateObj[];
}
export type MonthData = Record<number, DateObj[]>;
export type ParsedEventsType = ParsedEvents | ParsedEventsWithArray | MonthlyEvent;
export interface CalendarAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    updateCurrPos?: (currPos: number) => void;
    updateShowCurrTime?: () => void;
    updateScrollHeight?: (scrollHeight: number) => void;
    setParsedEvents?: (parsedEvents: ParsedEventsType) => void;
    cacheEventKeys?: (cachedKeys: Array<string>) => void;
    setRangeData?: (data: RangeData) => void;
    getRangeData?: () => RangeData;
    setWeeklyData?: (data: WeeklyData) => void;
    getWeeklyData?: () => WeeklyData;
    registerClickOutsideHandler?: (key: string, cb: () => void) => void;
    unregisterClickOutsideHandler?: () => void;
    setMonthlyData?: (data: MonthData) => void;
    getMonthlyData?: () => MonthData;
    notifyClose?: (e: any, key: string) => void;
    openCard?: (key: string, spacing: boolean) => void;
    setItemLimit?: (itemLimit: number) => void;
}
export default class CalendarFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<CalendarAdapter<P, S>, P, S> {
    raf: number;
    constructor(adapter: CalendarAdapter<P, S>);
    init(): void;
    destroy(): void;
    initCurrTime(): void;
    notifyScrollHeight(height: number): void;
    closeCard(e: any, key: string): void;
    _getDate(): any;
    showCard(e: any, key: string): void;
    formatCbValue(val: [Date, number, number, number] | [Date]): Date;
    /**
     *
     * find the location of showCurrTime red line
     */
    getCurrLocation(): void;
    getWeeklyData(value: Date, dateFnsLocale: Locale): WeeklyData;
    getRangeData(value: Date, dateFnsLocale: Locale): {
        month: string;
        week: Array<DateObj>;
    };
    getMonthlyData(value: Date, dateFnsLocale: Locale): MonthData;
    _parseEvents(events: EventObject[]): ParsedEventsWithArray;
    getParseDailyEvents(events: EventObject[], date: Date): ParsedEventsWithArray;
    parseDailyEvents(): void;
    _parseWeeklyEvents(events: ParsedEvents['allDay'], weekStart: Date): ParsedRangeEvent[][];
    _renderWeeklyAllDayEvent(events: ParsedRangeEvent[][]): ParsedRangeEvent[];
    parseWeeklyAllDayEvents(events: ParsedEvents['allDay']): ParsedRangeEvent[];
    getParsedWeeklyEvents(events: EventObject[]): ParsedEvents;
    parseWeeklyEvents(): void;
    pushDayEventIntoWeekMap(item: EventObject, index: number, map: Record<string, EventObject[]>): void;
    convertMapToArray(weekMap: Map<string, EventObject[]>, weekStart: Date): any[];
    getParseMonthlyEvents(itemLimit: number): MonthlyEvent;
    parseMonthlyEvents(itemLimit: number): void;
    _renderDisplayEvents(events: ParsedRangeEvent[][]): ParsedRangeEvent[] | ParsedRangeEvent[][];
    _parseRangeEvents(events: Map<string, EventObject[]>): ParsedRangeEvent[][];
    _renderRangeAllDayEvent(events: Array<Array<ParsedRangeEvent>>): ParsedRangeEvent[];
    parseRangeAllDayEvents(events: ParsedEvents['allDay']): ParsedRangeEvent[];
    getParsedRangeEvents(events: EventObject[]): ParsedEvents;
    parseRangeEvents(): void;
    checkWeekend(val: Date): boolean;
}
