$color-typography_default-text-default: var(--semi-color-text-0); // 默认文本颜色
$color-typography_secondary-text-default: var(--semi-color-text-1); // 稍次要文本颜色
$color-typography_tertiary-text-default: var(--semi-color-text-2); // 次要文本颜色
$color-typography_quaternary-text-default: var(--semi-color-text-3); // 最次要文本颜色
$color-typography_warning-text-default: var(--semi-color-warning); // 警告文本颜色
$color-typography_danger-text-default: var(--semi-color-danger); // 错误文本颜色
$color-typography_success-text-default: var(--semi-color-success); // 成功文本颜色

$color-typography_disabled-text-default: var(--semi-color-disabled-text); // 禁用文本颜色
$color-typography_mark-bg-default: var(--semi-color-primary-light-default); // 标记文本颜色
$color-typography_code-bg-default: var(--semi-color-fill-1); // 代码文本背景颜色
$color-typography_code-text-default: var(--semi-color-text-2); // 代码文本颜色

$color-typography_link-text-default: var(--semi-color-link); // 链接文本颜色 - 默认
$color-typography_link-text-visited: var(--semi-color-link-visited); // 链接文本颜色 - 已访问
$color-typography_link-text-hover: var(--semi-color-link-hover); // 链接文本颜色 - 悬浮
$color-typography_link-text-active: var(--semi-color-link-active); // 链接文本颜色 - 激活
$color-typography_link-text-disabled: var(--semi-color-link); // 链接文本颜色 - 禁用

$color-typography_copied-text-success: var(--semi-color-text-2); // 可复制文本颜色
$color-typography_copied-icon-success: var(--semi-color-success); // 可复制文本复制成功图标颜色
$color-typography_code-border-default: var(--semi-color-border); // 代码文本描边颜色


$font-typography_title-fontWeight: $font-weight-bold; // 标题文本字重
$font-typography_link-fontWeight: $font-weight-bold; // 链接文本字重
$font-typography_strong-fontWeight: $font-weight-bold; // 强调文本字重
$font-typography_paragraph_extended-lineHeight: 24px; // 宽松行距段落文本行高
$font-typography_normalText-regular-fontWeight: $font-weight-regular; // normal text 字重 - 正常
$font-typography_smallText-regular-fontWeight: $font-weight-regular; // small text 字重 - 正常
$font-typography_normalParagraph-regular-fontWeight: $font-weight-regular; // normal paragraph 字重 - 正常
$font-typography_smallParagraph-regular-fontWeight: $font-weight-regular; // small paragraph 字重 - 正常

$font-typography_title1-fontWeight: $font-typography_title-fontWeight; // 一级标题文本字重
$font-typography_title2-fontWeight: $font-typography_title-fontWeight; // 二级标题文本字重
$font-typography_title3-fontWeight: $font-typography_title-fontWeight; // 三级标题文本字重
$font-typography_title4-fontWeight: $font-typography_title-fontWeight; // 四级标题文本字重
$font-typography_title5-fontWeight: $font-typography_title-fontWeight; // 五级标题文本字重
$font-typography_title6-fontWeight: $font-typography_title-fontWeight; // 六级标题文本字重




$font-typography_title1-light-fontWeight: 200; // 一级标题文本字重 - 细
$font-typography_title2-light-fontWeight: 200; // 二级标题文本字重 - 细
$font-typography_title3-light-fontWeight: 200; // 三级标题文本字重 - 细
$font-typography_title4-light-fontWeight: 200; // 四级标题文本字重 - 细
$font-typography_title5-light-fontWeight: 200; // 五级标题文本字重 - 细
$font-typography_title6-light-fontWeight: 200; // 六级标题文本字重 - 细

$font-typography_title1-regular-fontWeight: 400; // 一级标题文本字重 - 正常
$font-typography_title2-regular-fontWeight: 400; // 二级标题文本字重 - 正常
$font-typography_title3-regular-fontWeight: 400; // 三级标题文本字重 - 正常
$font-typography_title4-regular-fontWeight: 400; // 四级标题文本字重 - 正常
$font-typography_title5-regular-fontWeight: 400; // 五级标题文本字重 - 正常
$font-typography_title6-regular-fontWeight: 400; // 六级标题文本字重 - 正常

$font-typography_title1-medium-fontWeight: 500; // 一级标题文本字重 - 中等
$font-typography_title2-medium-fontWeight: 500; // 二级标题文本字重 - 中等
$font-typography_title3-medium-fontWeight: 500; // 三级标题文本字重 - 中等
$font-typography_title4-medium-fontWeight: 500; // 四级标题文本字重 - 中等
$font-typography_title5-medium-fontWeight: 500; // 五级标题文本字重 - 中等
$font-typography_title6-medium-fontWeight: 500; // 六级标题文本字重 - 中等

$font-typography_title1-semibold-fontWeight: 600; // 一级标题文本字重 - 半粗
$font-typography_title2-semibold-fontWeight: 600; // 二级标题文本字重 - 半粗
$font-typography_title3-semibold-fontWeight: 600; // 三级标题文本字重 - 半粗
$font-typography_title4-semibold-fontWeight: 600; // 三级标题文本字重 - 半粗
$font-typography_title5-semibold-fontWeight: 600; // 三级标题文本字重 - 半粗
$font-typography_title6-semibold-fontWeight: 600; // 三级标题文本字重 - 半粗

$font-typography_title1-bold-fontWeight: 700; // 一级标题文本字重 - 粗
$font-typography_title2-bold-fontWeight: 700; // 二级标题文本字重 - 粗
$font-typography_title3-bold-fontWeight: 700; // 三级标题文本字重 - 粗
$font-typography_title4-bold-fontWeight: 700; // 三级标题文本字重 - 粗
$font-typography_title5-bold-fontWeight: 700; // 三级标题文本字重 - 粗
$font-typography_title6-bold-fontWeight: 700; // 三级标题文本字重 - 粗




$spacing-typography_iconPrefix-marginRight: 4px; // 带前缀文本图标右侧外边距
$spacing-typography_expandText-marginLeft: 8px; // 支持展开文本展开按钮左侧外边距
$spacing-typography_copyIcon-marginLeft: 4px; // 可复制文本复制图标左侧外边距
$spacing-typography_copyIcon-padding: 0; // 可复制文本复制图标内边距
$spacing-typography_title_h1-margin: 0; // 一级标题外边距
$spacing-typography_title_h2-margin: 0; // 二级标题外边距
$spacing-typography_title_h3-margin: 0; // 三级标题外边距
$spacing-typography_title_h4-margin: 0; // 四级标题外边距
$spacing-typography_title_h5-margin: 0; // 五级标题外边距
$spacing-typography_title_h6-margin: 0; // 六级标题外边距
$spacing-typography_title_paragraph-margin: 0; // 段落外边距

$width-typography_code-border: 1px; // 代码文本描边宽度
$width-typography_link-border: 1px; // 链接文本下划线宽度

$radius-typography_code: 2px; // 代码文本圆角
