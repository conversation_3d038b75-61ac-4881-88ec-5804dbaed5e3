import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { strings } from '../notification/constants';
export type NoticePosition = 'top' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
export type NoticeType = 'warning' | 'success' | 'info' | 'error' | 'default';
export type NoticeTheme = 'light' | 'normal';
export interface NoticeProps {
    duration?: number;
    id?: string;
    title?: any;
    content?: any;
    position?: NoticePosition;
    type?: NoticeType;
    onClick?: (e: any) => void;
    onClose?: () => void;
    onCloseClick?: (id: string) => void;
    showClose?: boolean;
    close?: (id: string) => void;
    zIndex?: number;
    icon?: any;
    getPopupContainer?: () => HTMLElement;
    theme?: NoticeTheme;
    onHookClose?: () => void;
    direction?: typeof strings.directions[number];
    className?: string;
    style?: any;
}
export interface NoticeState {
    visible: boolean;
}
export interface NoticeInstance extends NoticeProps {
    motion?: boolean;
}
export interface NoticeAdapter extends DefaultAdapter<NoticeProps, NoticeState> {
    notifyWrapperToRemove: (id: string) => void;
    notifyClose: () => void;
}
export default class NotificationFoundation extends BaseFoundation<NoticeAdapter> {
    _timer: ReturnType<typeof setTimeout> | null;
    _id: string | null;
    constructor(adapter: NoticeAdapter);
    init(): void;
    destroy(): void;
    _startCloseTimer(): void;
    close(e?: any): void;
    _clearCloseTimer(): void;
    restartCloseTimer(): void;
}
