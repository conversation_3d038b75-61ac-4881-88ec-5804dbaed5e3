import { strings } from './constants';
import type { BaseEllipsis } from './foundation';
export declare function equalWith(value: any, other: any, customizer?: (...args: any[]) => boolean): boolean;
export declare function getColumnKey(column: any, keyPropNames: any[]): any;
/**
 *
 * @param {Array<number>} arr
 * @param {number} [beginIndex] begin index, included
 * @param {number} [endIndex] end index, not included
 * @returns {number}
 */
export declare function arrayAdd(arr?: any[], beginIndex?: number, endIndex?: number): number;
export declare function isLastLeftFixed(columns: Record<string, any>[], column: Record<string, any>, checkKeys?: string[]): boolean;
export declare function isFirstFixedRight(columns: Record<string, any>[], column: Record<string, any>, checkKeys?: string[]): boolean;
export declare function isAnyFixed(columns: Record<string, any>[], fixedSet?: (string | boolean)[]): boolean;
export declare function isAnyFixedRight(columns: Record<string, any>[]): boolean;
export declare function isFixedLeft(column: Record<string, any>): boolean;
export declare function isFixedRight(column: Record<string, any>): boolean;
export declare function isFixed(column: Record<string, any>): boolean;
export declare function isInnerColumnKey(key: string | number): boolean;
export declare function isExpandedColumn(column: Record<string, any>): boolean;
export declare function isScrollbarColumn(column: Record<string, any>): boolean;
export declare function isSelectionColumn(column: Record<string, any>): boolean;
export declare function filterColumns(columns: Record<string, any>[], ignoreKeys?: string[]): Record<string, any>[];
/**
 * get width of scroll bar
 * @param {Array} columns
 * @returns {Number|undefined}
 */
export declare function getScrollbarColumnWidth(columns?: Record<string, any>[]): any;
export declare function getRecordKey(record: Record<string, any>, rowKey: string | number | ((record: any) => string | number)): any;
/**
 * Determine whether the expandedRowKeys includes a key (rowKey will be added to expandedRowKeys when the expand button is clicked)
 * @param {*} expandedRowKeys
 * @param {*} key
 */
export declare function isExpanded(expandedRowKeys: (string | number)[], key: string | number): boolean;
/**
 * Determine whether the selectedKeysSet includes the key
 * @param {Set} selectedRowKeysSet
 * @param {String} key
 */
export declare function isSelected(selectedRowKeysSet: Set<string | number>, key: string | number): boolean;
/**
 * Whether the key is included in the disabledRowKeysSet
 * @param {Set} disabledRowKeysSet
 * @param {String} key
 */
export declare function isDisabled(disabledRowKeysSet: Set<string | number>, key: string | number): boolean;
export declare function getRecord(data: any[], recordKey: string | number, rowKey: string | number | ((record: any) => string | number)): any;
export declare function getRecordChildren(record: Record<string, any>, childrenRecordName: string): any;
export declare function genExpandedRowKey(recordKey?: string, suffix?: string): string;
export declare function getDefaultVirtualizedRowConfig(size?: string, sectionRow?: boolean): {
    height?: number;
    minHeight?: number;
};
export declare function flattenColumns(cols: Record<string, any>[], childrenColumnName?: string): Record<string, any>[];
export declare function assignColumnKeys(columns: Record<string, any>[], childrenColumnName?: string, level?: number): Record<string, any>[];
export declare function sliceColumnsByLevel(columns: any[], targetLevel?: number, childrenColumnName?: string, currentLevel?: number): any[];
export declare function getColumnsByLevel(columns: Record<string, any>[], targetLevel?: number, targetColumns?: Record<string, any>[], currentLevel?: number, childrenColumnName?: string): Record<string, any>[];
export declare function getAllLevelColumns(columns: Record<string, any>[], childrenColumnName?: string): any[];
export declare function getColumnByLevelIndex(columns: Record<string, any>[], index: number, level?: number, childrenColumnName?: string): any;
export declare function findColumn(columns: Record<string, any>[], column: Record<string, any>, childrenColumnName?: string): any;
export declare function expandBtnShouldInRow(props: ExpandBtnShouldInRowProps): boolean;
export type ExpandBtnShouldInRowProps = {
    expandedRowRender: (record?: Record<string, any>, index?: number, expanded?: boolean) => any;
    dataSource: Record<string, any>[];
    hideExpandedColumn: boolean;
    childrenRecordName: string;
    rowExpandable: (record?: Record<string, any>) => boolean;
};
/**
 * merge query
 * @param {*} query
 * @param {*} queries
 */
export declare function mergeQueries(query: Record<string, any>, queries?: Record<string, any>[]): Record<string, any>[];
/**
 * Replace the width of the newColumns column with the width of the column after resize
 * @param {Object[]} columns columns retain the column width after resize
 * @param {Object[]} newColumns
 */
export declare function withResizeWidth(columns: Record<string, any>[], newColumns: Record<string, any>[]): Record<string, any>[];
/**
 * Pure function version of the same function in table foundation
 * This is not accessible in getDerivedStateFromProps, so fork one out
 */
export declare function getAllDisabledRowKeys({ dataSource, getCheckboxProps, childrenRecordName, rowKey }: GetAllDisabledRowKeysProps): (string | number)[];
export interface GetAllDisabledRowKeysProps {
    dataSource: Record<string, any>[];
    getCheckboxProps: (record?: Record<string, any>) => any;
    childrenRecordName?: string;
    rowKey?: string | number | ((record: Record<string, any>) => string | number);
}
export declare function warnIfNoDataIndex(column: Record<string, any>): void;
/**
 * Whether is tree table
 */
export declare function isTreeTable({ dataSource, childrenRecordName }: {
    dataSource: Record<string, any>;
    childrenRecordName?: string;
}): boolean;
export declare function getRTLAlign(align: typeof strings.ALIGNS[number], direction?: 'ltr' | 'rtl'): typeof strings.ALIGNS[number];
export declare function getRTLFlexAlign(align: typeof strings.ALIGNS[number], direction?: 'ltr' | 'rtl'): typeof strings.JUSTIFY_CONTENT[number];
export declare function shouldShowEllipsisTitle(ellipsis: BaseEllipsis): boolean;
