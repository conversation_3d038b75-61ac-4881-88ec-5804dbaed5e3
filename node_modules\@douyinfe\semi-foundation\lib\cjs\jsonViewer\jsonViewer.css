/* shadow */
/* sizing */
/* spacing */
.semi-json-viewer-background {
  background-color: var(--semi-color-default);
}
.semi-json-viewer-string-key {
  color: rgba(var(--semi-red-5), 1);
}
.semi-json-viewer-string-value {
  color: rgba(var(--semi-blue-5), 1);
}
.semi-json-viewer-keyword {
  color: rgba(var(--semi-blue-5), 1);
}
.semi-json-viewer-number {
  color: rgba(var(--semi-green-5), 1);
}
.semi-json-viewer-delimiter-comma {
  color: rgba(var(--semi-blue-6), 1);
}
.semi-json-viewer-delimiter-bracket-0 {
  color: rgba(var(--semi-blue-7), 1);
}
.semi-json-viewer-delimiter-bracket-1 {
  color: rgba(var(--semi-green-7), 1);
}
.semi-json-viewer-delimiter-bracket-2 {
  color: rgba(var(--semi-orange-7), 1);
}
.semi-json-viewer-delimiter-array-0 {
  color: rgba(var(--semi-blue-7), 1);
}
.semi-json-viewer-delimiter-array-1 {
  color: rgba(var(--semi-green-7), 1);
}
.semi-json-viewer-delimiter-array-2 {
  color: rgba(var(--semi-orange-7), 1);
}
.semi-json-viewer-search-result {
  background-color: rgba(var(--semi-green-2), 1);
}
.semi-json-viewer-current-search-result {
  background-color: rgba(var(--semi-yellow-4), 1) !important;
}
.semi-json-viewer-folding-icon {
  opacity: 0.7;
  transition: opacity 0.8s;
  color: rgba(var(--semi-blue-7), 1);
}
.semi-json-viewer-view-line {
  font-family: Menlo, Firecode, Monaco, "Courier New", monospace;
  font-weight: normal;
  font-size: 12px;
  font-feature-settings: "liga" 0, "calt" 0;
  font-variation-settings: normal;
  letter-spacing: 0px;
  color: #237893;
  word-break: break-all !important;
  white-space: pre-wrap !important;
}
.semi-json-viewer-line-number {
  font-family: Menlo, Firecode, Monaco, "Courier New", monospace;
  font-weight: normal;
  font-size: 12px;
  font-feature-settings: "liga" 0, "calt" 0;
  font-variation-settings: normal;
  letter-spacing: 0px;
  color: rgba(var(--semi-grey-5), 1);
  text-align: center;
  width: 50px;
  user-select: none;
  word-wrap: normal !important;
  overflow-wrap: normal !important;
}
.semi-json-viewer-content-container {
  scrollbar-width: none; /* 隐藏滚动条（Firefox） */
  -ms-overflow-style: none; /* 隐藏滚动条（IE 和 Edge） */
}
.semi-json-viewer-content-container::-webkit-scrollbar {
  display: none; /* 隐藏滚动条（Webkit 浏览器） */
}
.semi-json-viewer-search-bar-container {
  width: 458px;
  box-sizing: border-box;
  border: 1px solid var(--semi-color-border);
  border-radius: var(--semi-border-radius-small);
  display: flex;
  flex-direction: column;
  padding: 8px;
  gap: 8px;
  background-color: var(--semi-color-bg-0);
}
.semi-json-viewer-search-bar {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}
.semi-json-viewer-search-bar-input {
  width: 200px !important;
  flex-shrink: 0;
}
.semi-json-viewer-search-bar .semi-button-group {
  flex-wrap: nowrap;
}
.semi-json-viewer-search-bar .semi-button:nth-of-type(1) {
  width: 40px;
}
.semi-json-viewer-search-bar .semi-button:nth-of-type(2) {
  width: 40px;
}
.semi-json-viewer-replace-bar {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}
.semi-json-viewer-replace-bar-input {
  width: 261px;
}
.semi-json-viewer-replace-bar .semi-button:nth-of-type(1) {
  width: 52px;
}
.semi-json-viewer-replace-bar .semi-button:nth-of-type(2) {
  width: 80px;
}
.semi-json-viewer-search-options {
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
  padding-inline-start: 0;
  margin-block-start: 0;
  margin-block-end: 0;
  gap: 8px;
}
.semi-json-viewer-search-options-item {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: var(--semi-border-radius-small);
  color: var(--semi-color-text-2);
}
.semi-json-viewer-search-options-item:hover {
  background-color: var(--semi-color-default);
}
.semi-json-viewer-search-options-item-active {
  color: var(--semi-color-primary);
  background-color: var(--semi-color-primary-light-default);
}
.semi-json-viewer-complete-suggestions-container {
  border-radius: var(--semi-border-radius-medium);
  background-color: var(--semi-color-bg-3);
  box-shadow: var(--semi-shadow-elevated);
  z-index: 1000;
  min-width: 200px;
  max-width: 400px;
  list-style: none;
  padding: 4px 0;
}
.semi-json-viewer-complete-container {
  position: absolute;
  z-index: 1000;
}
.semi-json-viewer-complete-suggestions-item {
  padding: 8px 16px;
  color: var(--semi-color-text-0);
  cursor: pointer;
}
.semi-json-viewer-error {
  text-decoration: underline wavy var(--semi-color-danger);
  text-decoration-thickness: 1px;
  text-underline-position: under;
}