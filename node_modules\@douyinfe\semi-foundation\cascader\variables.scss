$radius-cascader: var(--semi-border-radius-small); // 级联选择菜单圆角
$radius-cascader_option_empty: var(--semi-border-radius-medium); // 级联选择菜单空状态圆角

$color-cascader_default-border-default: transparent; // 级联选择描边颜色 - 默认
$color-cascader_default-border-hover: transparent; // 级联选择描边颜色 - 悬浮
$color-cascader_default-border-focus: var(--semi-color-focus-border); // 级联选择描边颜色 - 选中
$color-cascader_default-bg-default: var(--semi-color-fill-0); // 级联选择菜单项背景颜色 - 默认
$color-cascader_default-bg-hover: var(--semi-color-fill-1); // 级联选择菜单项背景颜色 - 悬浮
$color-cascader_default-bg-active: var(--semi-color-fill-2); // 级联选择菜单项背景颜色 - 按下
$color-cascader_option_list-border-default: var(--semi-color-fill-0); // 级联选择各级菜单分割线颜色
$color-cascader_option_main-text-default: var(--semi-color-text-0); // 级联选择菜单项文字颜色
$color-cascader_option-bg-hover: var(--semi-color-fill-0); // 级联选择菜单项背景颜色 - 悬浮
$color-cascader_option-bg-active: var(--semi-color-fill-1); // 级联选择菜单项背景颜色 - 按下
$color-cascader_option-bg-selected: var(--semi-color-primary-light-default); // 级联选择菜单项背景颜色 - 命中
$color-cascader_option_disabled-text-default: var(--semi-color-disabled-text); // 级联选择菜单项文字颜色 - 禁用
$color-cascader_option_disabled-bg-hover: transparent; // 级联选择菜禁用单项背景颜色 - 悬浮
$color-cascader_option_disabled-bg-active: transparent; // 级联选择菜禁用单项背景颜色 - 按下
$color-cascader_tag_disabled-bg-default: transparent; // 级联选择菜禁用Tag背景颜色

// warning，same as Input
$color-cascader_warning-bg-default: var(--semi-color-warning-light-default); // 级联选择触发器警告背景颜色
$color-cascader_warning-border-default: var(--semi-color-warning-light-default); // 级联选择触发器警告描边颜色

$color-cascader_warning-bg-hover: var(--semi-color-warning-light-hover); // 级联选择触发器警告背景颜色 - 悬浮
$color-cascader_warning-border-hover: var(--semi-color-warning-light-hover); // 级联选择触发器警告描边颜色 - 悬浮

$color-cascader_warning-bg-focus: var(--semi-color-warning-light-default); // 级联选择触发器警告背景颜色 - 选中
$color-cascader_warning-border-focus: var(--semi-color-warning); // 级联选择触发器警告描边颜色 - 选中

$color-cascader_warning-bg-active: var(--semi-color-warning-light-active); // 级联选择触发器警告背景颜色 - 按下
$color-cascader_warning-border-active: var(--semi-color-warning-light-active); // 级联选择触发器警告描边颜色 - 按下

// error，same as Input
$color-cascader_danger-bg-default: var(--semi-color-danger-light-default); // 级联选择触发器危险背景颜色
$color-cascader_danger-border-default: var(--semi-color-danger-light-default); // 级联选择触发器危险描边颜色

$color-cascader_danger-bg-hover: var(--semi-color-danger-light-hover); // 级联选择触发器危险背景颜色 - 悬浮
$color-cascader_danger-border-hover: var(--semi-color-danger-light-hover); // 级联选择触发器危险描边颜色 - 悬浮

$color-cascader_danger-bg-focus: var(--semi-color-danger-light-default); // 级联选择触发器危险背景颜色 - 选中
$color-cascader_danger-border-focus: var(--semi-color-danger); // 级联选择触发器危险描边颜色 - 选中

$color-cascader_danger-bg-active: var(--semi-color-danger-light-active); // 级联选择触发器危险背景颜色 - 按下
$color-cascader_danger-border-active: var(--semi-color-danger-light-active); // 级联选择触发器危险描边颜色 - 按下

$color-cascader_prefix_suffix_text-default: var(--semi-color-text-2); // 级联选择前后缀文字颜色

$spacing-cascader_selection-paddingLeft: 12px; // 级联选择触发器左侧内边距 - 默认
$spacing-cascader_selection-paddingRight: 12px; // 级联选择触发器右侧内边距 - 默认
$spacing-cascader_small_selection-paddingLeft: 12px;  // 级联选择触发器左侧内边距 - 小尺寸
$spacing-cascader_small_selection-paddingRight: 12px;  // 级联选择触发器右侧内边距 - 小尺寸
$spacing-cascader_large_selection-paddingLeft: 12px;  // 级联选择触发器左侧内边距 - 大尺寸
$spacing-cascader_large_selection-paddingRight: 12px;  // 级联选择触发器右侧内边距 - 大尺寸

$spacing-cascader_selection_multiple-paddingLeft: $spacing-extra-tight; // 级联选择触发器多选时左侧内边距 - 默认
$spacing-cascader_selection_multiple-paddingRight: $spacing-extra-tight; // 级联选择触发器多选时右侧内边距 - 默认
$spacing-cascader_small_selection_multiple-paddingLeft: $spacing-extra-tight; // 级联选择触发器多选时左侧内边距 - 小尺寸
$spacing-cascader_small_selection_multiple-paddingRight: $spacing-extra-tight; // 级联选择触发器多选时左侧内边距 - 小尺寸
$spacing-cascader_large_selection_multiple-paddingLeft: $spacing-extra-tight; // 级联选择触发器多选时左侧内边距 - 大尺寸
$spacing-cascader_large_selection_multiple-paddingRight: $spacing-extra-tight; // 级联选择触发器多选时左侧内边距 - 大尺寸

$spacing-cascader_selection_tag-marginLeft: $spacing-none; // 级联选择触发器多选时标签的水平左外边距
$spacing-cascader_selection_tag-marginRight: $spacing-super-tight; // 级联选择触发器多选时标签的水平右外边距
$spacing-cascader_selection_tag-marginY: 1px; // 级联选择触发器多选时标签的垂直外边距
$spacing-cascader_selection_tagInput-marginLeft: - $spacing-extra-tight; // 级联选择触发器多选搜索时 TagInput 的左外边距
$spacing-cascader_selection_input-marginLeft: $spacing-extra-tight; // 级联选择触发器多选搜索时输入框的左外边距
$spacing-cascader_selection_n-marginRight: $spacing-extra-tight; // 超出 maxTagCount 后，+n 的右外边距
$spacing-cascader_option-icon-marginLeft: 8px; // 级联选择菜单项图标左侧外边距

$color-cascader_selection_n-text-default: var(--semi-color-text-0); // 超出 maxTagCount 后，+n 的文字默认颜色
$color-cascader_selection_n-text-disabled: var(--semi-color-disabled-text); // 超出 maxTagCount 后，+n 的文字disabled颜色
$color-cascader_selection_text_inactive: var(--semi-color-text-2); // 级联选择单选inpu输入框和text并存时，text颜色
$color-cascader_selection-text-default: var(--semi-color-text-0); // 级联选择选中项文字颜色
$color-cascader_placeholder-text-default: var(--semi-color-text-2); // 级联选择未选中项文字颜色
$color-cascader-icon-default: var(--semi-color-text-2); // 级联选择图标颜色 - 默认
$color-cascader-icon-hover: var(--semi-color-primary-hover); // 级联选择图标颜色 - 悬浮
$color-cascader-icon-active: var(--semi-color-primary-active); // 级联选择图标颜色 - 按下

$color-cascader_label-text-default: var(--semi-color-text-2); // 级联选择 prefix 文字颜色
$color-cascader_option-icon-default: $color-cascader-icon-default; // 级联选择菜单项图标颜色


$color-cascader_input_disabled-bg-default: var(--semi-color-disabled-fill); // 级联选择禁用态背景颜色
$color-cascader_input_disabled-bg-hover: var(--semi-color-disabled-fill); // 级联选择禁用态背景颜色 - 悬浮
$color-cascader_input_disabled-text-default: var(--semi-color-disabled-text); // 级联选择禁用态文字颜色
$color-cascader_input_disabled-icon-default: var(--semi-color-disabled-text); // 级联选择禁用态图标颜色
$color-cascader_search-border-default: var(--semi-color-fill-0); // 级联选择搜索描边颜色
$color-cascader_option_empty-text-default: var(--semi-color-disabled-text); // 级联选择空状态文字颜色
$color-cascader_input-bg-default: transparent; // 级联选择输入框背景颜色
$color-cascader_input-border-default: none; // 级联选择输入框描边颜色
$color-cascader_select_highlight: var(--semi-color-primary); // 级联选择器搜索命中后文字高亮的颜色

$font-cascader_select-fontWeight: $font-weight-bold; // 级联选择菜单项字重 - 选中
$font-cascader-fontWeight: $font-weight-regular; // 级联选择菜单项字重 - 未选中
$font-cascader_label-fontWeight: 600;
$font-cascader_selection_n-fontSize: $font-size-small; // 超出 maxTagCount 后，+n 的文字尺寸
$font-cascader_prefix_suffix_fontWeight: $font-weight-bold; // 级联选择 prefix/suffix 文字字重

$width-cascader-border: 1px; // 级联选择触发器描边宽度
$width-cascader_option_list-border: 1px; // 级联选择各级菜单分割线宽度
$width-cascader:  80px; // 级联选择触发器最小宽度
$height-cascader_default:  $height-control-default; // 级联选择触发器高度 - 默认
$height-cascader_small: $height-control-small; // 级联选择触发器高度 - 小尺寸
$height-cascader_large: $height-control-large; // 级联选择触发器高度 - 大尺寸
$width-cascader_hover-border: $width-cascader-border; // 级联选择触发器描边宽度 - 悬浮
$width-cascader_focus-border: $border-thickness-control-focus; // 级联选择触发器描边宽度 - 选中态
$width-cascader_search-border: 1px; // 级联选择触搜索描边宽度
$width-cascader-icon: 32px; // 级联选择触发器图标尺寸
$width-cascader-option-icon: 16px; // 级联选择选项面板图标尺寸
$width-cascader_option:  150px; // 级联选择各级菜单宽度
$height-cascader_option_list: 180px; // 级联选择菜单高度
$height-cascader_selection_tagInput_wrapper_small: 22px;  //级联选择多选搜索时搜索框最小高度 - 小尺寸
$height-cascader_selection_tagInput_wrapper_default: 30px;  //级联选择多选搜索时搜索框最小高度 - 默认尺寸
$height-cascader_selection_tagInput_wrapper_large: 38px;  //级联选择多选搜索时搜索框最小高度 - 大尺寸
$height-cascader_selection_wrapper_small: 22px;  //级联选择单选搜索时搜索框高度 - 小尺寸
$height-cascader_selection_wrapper: 30px; // 级联选择单选搜索时搜索框高度 - 默认尺寸
$height-cascader_selection_wrapper_large: 38px;  //级联选择单选搜索时搜索框高度 - 大尺寸

$spacing-cascader_text-marginX: $spacing-base-tight; // 级联选择 prefix/suffix 文字水平内间距
$spacing-cascader_icon-marginX: $spacing-tight; // 级联选择 prefix/suffix 图标水平内间距
$spacing-cascader_label_checkbox-marginRight: $spacing-tight; // 级联选择 checkbox 的右间距
$spacing-cascader_selection_n-paddingX: $spacing-tight; // 级联选择 +N 的水平间距

$spacing-cascader_flatten_list-paddingRight: 64px; // 级联选择搜索结果菜单右侧内边距
$spacing-cascader_empty_icon-marginRight: 8px;  // 级联选择搜索结果菜单

$spacing-cacader_option_list-paddingY: 4px; // 级联选择菜单项垂直内边距
$spacing-cacader_option_list-paddingX: 0px; // 级联选择菜单项水平内边距

$spacing-cascader_search-paddingX: 12px; // 级联选择搜索水平内边距
$spacing-cascader_search-paddingY: 8px; // 级联选择搜索垂直内边距
$spacing-cascader_option_empty-paddingY: 8px; // 级联选择空结果垂直内边距
$spacing-cascader_option_empty-paddingX: 12px;  // 级联选择空结果水平内边距
$spacing-cascader_option-paddingTop: 8px; // 级联选择菜单项顶部内边距
$spacing-cascader_option-paddingBottom: 8px; // 级联选择菜单项底部内边距
$spacing-cascader_option-paddingLeft: 12px; // 级联选择菜单项左侧内边距
$spacing-cascader_option-paddingRight: 16px; // 级联选择菜单项右侧内边距



