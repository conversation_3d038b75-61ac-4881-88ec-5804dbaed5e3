import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface ItemProps {
    text?: any;
    itemKey?: any;
    icon?: any;
    toggleIcon?: string;
    indent?: boolean | number;
    isCollapsed?: boolean;
    isSubNav?: boolean;
    link?: string;
    linkOptions?: Record<string, any>;
    disabled?: boolean;
}
export type ItemKey = string | number;
export interface SelectedItemProps<Props = ItemProps> {
    itemKey: ItemKey;
    text?: any;
    selectedKeys?: ItemKey[];
    selectedItems?: Props[];
    domEvent?: any;
}
export interface ItemAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    cloneDeep(value: any, customizer?: (value: any) => void): any;
    updateTooltipShow(showTooltip: boolean): void;
    updateSelected(selected: boolean): void;
    updateGlobalSelectedKeys(keys: ItemKey[]): void;
    getSelectedKeys(): ItemKey[];
    getSelectedKeysIsControlled(): boolean;
    notifyGlobalOnSelect(item: SelectedItemProps): void;
    notifyGlobalOnClick(item: SelectedItemProps): void;
    notifyClick(item: SelectedItemProps): void;
    notifyMouseEnter(e: any): void;
    notifyMouseLeave(e: any): void;
    getIsCollapsed(): boolean;
    getSelected(): boolean;
    getIsOpen(): boolean;
}
export default class ItemFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<ItemAdapter<P, S>, P, S> {
    _timer: number;
    _mounted: boolean;
    constructor(adapter: ItemAdapter<P, S>);
    init(): void;
    destroy(): void;
    isValidKey(itemKey: string): boolean;
    handleClick(e: any): void;
    /**
     * A11y: simulate item click
     */
    handleKeyPress(e: any): void;
}
