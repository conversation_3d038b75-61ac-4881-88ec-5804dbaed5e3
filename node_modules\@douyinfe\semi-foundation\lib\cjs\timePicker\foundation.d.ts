import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export type Position = 'top' | 'topLeft' | 'topRight' | 'left' | 'leftTop' | 'leftBottom' | 'right' | 'rightTop' | 'rightBottom' | 'bottom' | 'bottomLeft' | 'bottomRight' | 'leftTopOver' | 'rightTopOver';
export interface TimePickerAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    togglePanel: (show: boolean) => void;
    registerClickOutSide: () => void;
    setInputValue: (inputValue: string, cb?: () => void) => void;
    unregisterClickOutSide: () => void;
    notifyOpenChange: (open: boolean) => void;
    notifyChange(value: Date | Date[], input: string | string[]): void;
    notifyChange(input: string | string[], value: Date | Date[]): void;
    notifyFocus: (e: any) => void;
    notifyBlur: (e: any) => void;
    isRangePicker: () => boolean;
}
declare class TimePickerFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<TimePickerAdapter<P, S>, P, S> {
    constructor(adapter: TimePickerAdapter<P, S>);
    init(): void;
    getPosition(): Position;
    isDisabledHMS({ hours, minutes, seconds }: {
        hours: number;
        minutes: number;
        seconds: number;
    }): boolean;
    isValidTimeZone(timeZone: string | number): boolean;
    getDefaultFormatIfNeed(): string;
    /**
     * User input value => save timestamp
     */
    initDataFromDefaultValue(): void;
    getValidFormat(validFormat?: string): string;
    handlePanelChange(result: {
        isAM: boolean;
        value: string;
        timeStampValue: number;
    }, index?: number): void;
    refreshProps(props?: any): void;
    handleFocus(e: any): void;
    setPanel(open: boolean): void;
    destroy(): void;
    handlePanelOpen(): void;
    handlePanelClose(clickedOutside: boolean, e: any): void;
    handleVisibleChange(visible: boolean): void;
    handleInputChange(input: string): void;
    doValidate(args: string | Array<Date>): boolean;
    validateStr(inputValue?: string): boolean;
    validateDates(dates?: Array<Date>): boolean;
    handleInputBlur(e: any): void;
    formatValue(dates: Date[]): string;
    parseInput(str: string): Date[];
    parseValue(value?: string | Date | Array<string | Date>): Date[];
    _notifyChange(value: Date[], inputValue: string): void;
    _hasChanged(dates?: Date[], oldDates?: Date[]): boolean;
}
export default TimePickerFoundation;
