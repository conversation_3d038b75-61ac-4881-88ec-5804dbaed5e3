$color-badge_default-border-default: var(--semi-color-bg-1); // 描边颜色 - 默认
$color-badge_default-bg-default: var(--semi-color-bg-1); // 背景颜色 - 默认
$color-badge_default_light-bg-default: var(--semi-color-bg-2); // 背景颜色 - 浅版

$color-badge_default-text-default: var(--semi-color-bg-2); // 文字颜色 - 默认

$color-badge_primary_solid-bg-default: var(--semi-color-primary); // 背景颜色 - 主色
$color-badge_primary_light-bg-default: var(--semi-color-primary-light-default); // 背景颜色 - 浅版主色
$color-badge_primary_light-text-default: var(--semi-color-primary); // 文字颜色 - 浅版主色
$color-badge_primary_inverted-text-default: var(--semi-color-primary); // 文字颜色 - 白底

$color-badge_secondary_solid-bg-default: var(--semi-color-secondary); // 背景颜色 - 次要
$color-badge_secondary_light-bg-default: var(--semi-color-secondary-light-default); // 背景颜色 - 浅版次要
$color-badge_secondary_light-text-default: var(--semi-color-secondary); // 文字颜色 - 浅版次要
$color-badge_secondary_inverted-text-default: var(--semi-color-secondary); // 文字颜色 - 白底

$color-badge_tertiary_solid-bg-default: var(--semi-color-tertiary); // 背景颜色 - 第三
$color-badge_tertiary_light-bg-default: var(--semi-color-tertiary-light-default); // 背景颜色 - 第三
$color-badge_tertiary_light-text-default: var(--semi-color-tertiary); // 文字颜色 - 浅版第三
$color-badge_tertiary_inverted-text-default: var(--semi-color-tertiary); // 文字颜色 - 白底

$color-badge_danger_solid-bg-default: var(--semi-color-danger); // 背景颜色 - 危险
$color-badge_danger_light-bg-default: var(--semi-color-danger-light-default); // 背景颜色 - 浅版危险
$color-badge_danger_light-text-default: var(--semi-color-danger); // 文字颜色 - 浅版危险
$color-badge_danger_inverted-text-default: var(--semi-color-danger); // 文字颜色 - 白底

$color-badge_warning_solid-bg-default: var(--semi-color-warning); // 背景颜色 - 警告
$color-badge_warning_light-bg-default: var(--semi-color-warning-light-default); // 背景颜色 - 浅版警告
$color-badge_warning_light-text-default: var(--semi-color-warning); // 文字颜色 - 浅版危险
$color-badge_warning_inverted-text-default: var(--semi-color-warning); // 文字颜色 - 白底

$color-badge_success_solid-bg-default: var(--semi-color-success); // 背景颜色 - 成功
$color-badge_success_light-bg-default: var(--semi-color-success-light-default); // 背景颜色 - 浅版成功
$color-badge_success_light-text-default: var(--semi-color-success); // 文字颜色 - 浅版成功
$color-badge_success_inverted-text-default: var(--semi-color-success); // 文字颜色 - 白底

$width-badge_dot: 8px; // 点状徽标宽度
$height-badge_dot: 8px; // 点状徽标高度
$radius-badge_dot: var(--semi-border-radius-circle); // 点状徽标圆角

$height-badge_count: 18px; // 数字徽标宽度
$spacing-badge_count-paddingY: 0px; // 数字徽标上下内边距
$spacing-badge_count-paddingX: 4px; // 数字徽标左右内边距

$width-badge-border: 1px; // 描边宽度
$z-badge: 1; // 徽标 z-index
$z-badge_light-bg: -1; // 徽标浅色背景 z-index
