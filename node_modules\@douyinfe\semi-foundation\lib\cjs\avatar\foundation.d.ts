import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface AvatarAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    notifyImgState(isImgExist: boolean): void;
    notifyLeave(event: any): void;
    notifyEnter(event: any): void;
    setFocusVisible: (focusVisible: boolean) => void;
    setScale: (scale: number) => void;
    getAvatarNode: () => HTMLSpanElement;
}
export default class AvatarFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<AvatarAdapter<P, S>, P, S> {
    constructor(adapter: AvatarAdapter<P, S>);
    init(): void;
    destroy(): void;
    handleImgLoadError(): void;
    handleEnter(e: any): void;
    handleLeave(e: any): void;
    handleFocusVisible: (event: any) => void;
    handleBlur: () => void;
    changeScale: () => void;
}
