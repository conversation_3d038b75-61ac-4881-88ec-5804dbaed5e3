"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.zoomingExits = exports.zoomingEntrances = exports.specials = exports.slidingExits = exports.slidingEntrances = exports.rotatingExits = exports.rotatingEntrances = exports.lightspeed = exports.flippers = exports.fadingExits = exports.fadingEntrances = exports.bouncingExits = exports.bouncingEntrances = exports.attentionSeekers = void 0;
const attentionSeekers = exports.attentionSeekers = ['bounce', 'flash', 'headShake', 'heartBeat', 'jello', 'pulse', 'rubberBand', 'shake', 'swing', 'tada', 'wobble'];
const bouncingEntrances = exports.bouncingEntrances = ['bounceIn', 'bounceInDown', 'bounceInLeft', 'bounceInRight', 'bounceInUp'];
const bouncingExits = exports.bouncingExits = ['bounceOut', 'bounceOutDown', 'bounceOutLeft', 'bounceOutRight', 'bounceOutUp'];
const fadingEntrances = exports.fadingEntrances = ['fadeIn', 'fadeInDown', 'fadeInDownBig', 'fadeInLeft', 'fadeInLeftBig', 'fadeInRight', 'fadeInRightBig', 'fadeInUp', 'fadeInUpBig'];
const fadingExits = exports.fadingExits = ['fadeOut', 'fadeOutDown', 'fadeOutDownBig', 'fadeOutLeft', 'fadeOutLeftBig', 'fadeOutRight', 'fadeOutRightBig', 'fadeOutUp', 'fadeOutUpBig'];
const flippers = exports.flippers = ['flip', 'flipInX', 'flipInY', 'flipOutX', 'flipOutY'];
const lightspeed = exports.lightspeed = ['lightSpeedIn', 'lightSpeedOut'];
const rotatingEntrances = exports.rotatingEntrances = ['rotateIn', 'rotateInDownLeft', 'rotateInDownRight', 'rotateInUpLeft', 'rotateInUpRight'];
const rotatingExits = exports.rotatingExits = ['rotateOut', 'rotateOutDownLeft', 'rotateOutDownRight', 'rotateOutUpLeft', 'rotateOutUpRight'];
const slidingEntrances = exports.slidingEntrances = ['slideInDown', 'slideInLeft', 'slideInRight', 'slideInUp'];
const slidingExits = exports.slidingExits = ['slideOutDown', 'slideOutLeft', 'slideOutRight', 'slideOutUp'];
const specials = exports.specials = ['hinge', 'jackInTheBox', 'rollIn', 'rollOut'];
const zoomingEntrances = exports.zoomingEntrances = ['zoomIn', 'zoomInDown', 'zoomInLeft', 'zoomInRight', 'zoomInUp'];
const zoomingExits = exports.zoomingExits = ['zoomOut', 'zoomOutDown', 'zoomOutLeft', 'zoomOutRight', 'zoomOutUp'];