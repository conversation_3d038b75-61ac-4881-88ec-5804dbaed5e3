$module: #{$prefix}-layout;

.#{$module} {
  
    display: flex;
    flex: auto;
    flex-direction: column;
    min-height: auto;

    &,
    &-header,
    &-footer,
    &-content,
    &-sider,
    &-sider-children {
        box-sizing: border-box;
    }

    &-header,
    &-footer {
        flex: 0 0 auto;
    }

    &-content {
        flex: auto;
        min-height: auto;
    }

    &-sider {
        position: relative;
        min-width: auto;
    
        &-children {
            height: 100%;
            margin-top: -.1px;
            padding-top: .1px;
        }
    }
}

.#{$module}-has-sider {
    flex-direction: row;
    & > .#{$module},
    & > .#{$module}-content {
        overflow-x: hidden;
    }
}

@import "./rtl.scss";
