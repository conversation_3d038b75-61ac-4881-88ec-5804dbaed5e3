import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { ModalProps } from '../modal/modalFoundation';
export interface ModalContentProps extends ModalProps {
    onClose: (e: any) => void;
    getContainerContext: () => any;
    isFullScreen?: boolean;
    contentClassName?: string;
    maskClassName?: string;
    onAnimationEnd?: (e: any) => void;
    maskExtraProps?: Record<string, any>;
    contentExtraProps?: Record<string, any>;
}
export interface ModalContentState {
    dialogMouseDown: boolean;
    prevFocusElement: HTMLElement;
}
export interface ModalContentAdapter extends DefaultAdapter<ModalContentProps, ModalContentState> {
    notifyClose: (e: any) => void;
    notifyDialogMouseDown: () => void;
    notifyDialogMouseUp: () => void;
    addKeyDownEventListener: () => void;
    removeKeyDownEventListener: () => void;
    getMouseState: () => boolean;
    modalDialogFocus: () => void;
    modalDialogBlur: () => void;
    prevFocusElementReFocus: () => void;
}
export default class ModalContentFoundation extends BaseFoundation<ModalContentAdapter> {
    constructor(adapter: ModalContentAdapter);
    destroy(): void;
    handleDialogMouseDown(): void;
    handleMaskMouseUp(): void;
    handleKeyDown: (e: any) => void;
    handleKeyDownEventListenerMount(): void;
    handleKeyDownEventListenerUnmount(): void;
    getMouseState(): void;
    handleMaskClick(e: any): void;
    close(e: any): void;
    modalDialogFocus(): void;
    modalDialogBlur(): void;
    prevFocusElementReFocus(): void;
}
