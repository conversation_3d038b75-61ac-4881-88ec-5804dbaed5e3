import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export interface CarouselAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    notifyChange: (activeIndex: number, preIndex: number) => void;
    setNewActiveIndex: (activeIndex: number) => void;
    setPreActiveIndex: (activeIndex: number) => void;
    setIsReverse: (isReverse: boolean) => void;
    setIsInit: (isInit: boolean) => void;
    getChildren: () => any[];
}
declare class CarouselFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<CarouselAdapter<P, S>, P, S> {
    constructor(adapter: CarouselAdapter<P, S>);
    _interval: any;
    _forcePlay: boolean;
    setForcePlay(forcePlay: boolean): void;
    play(interval: number): void;
    stop(): void;
    goTo(activeIndex: number): void;
    next(): void;
    prev(): void;
    destroy(): void;
    _unregisterInterval(): void;
    _notifyChange(activeIndex: number): void;
    getValidIndex(index: number): number;
    getSwitchingTime(): number;
    getIsControlledComponent(): boolean;
    handleAutoPlay(): void;
    handleKeyDown(event: any): void;
    onIndicatorChange(activeIndex: number): void;
    handleNewActiveIndex(activeIndex: number): void;
    getDefaultActiveIndex(): number;
}
export default CarouselFoundation;
