@import "./variables.scss";


$module: #{$prefix}-colorPicker;


.#{$module} {

    &-colorChooseArea{

        /**
         *  Referrer from https://github.com/web-padawan/vanilla-colorful/blob/5d219ee360ae2f29534864b28ca9e6074233b9ce/src/lib/styles/saturation.css
         */
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
        position: relative;
        flex-grow: 1;
        border-color: transparent;
        border-radius: $radius-colorPicker-topLeft $radius-colorPicker-topRight $radius-colorPicker-bottomLeft $radius-colorPicker-bottomRight;
        background-image: linear-gradient(to top, #000, rgba(0, 0, 0, 0)), linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
    }


    &-handle{
        border-radius: $radius-colorPicker-handle;
        border: $width-colorPicker_handle-border solid $color-colorPicker_handle-border;
        position: absolute;
        box-sizing: border-box;
        cursor: grab;
    }




    &-alphaSlider{
        position: relative;
        cursor: pointer;
        margin-top: $spacing-colorPicker_slider-marginTop;
        border-radius: $radius-colorPicker-handle;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><rect x="8" width="8" height="8"/><rect y="8" width="8" height="8"/></svg>');


        .#{$module}-alphaSliderInner{
            width: 100%;
            height: 100%;
            border-radius: $radius-colorPicker-alphaSliderInner;
        }

        .#{$module}-alphaHandle{
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><rect x="8" width="8" height="8"/><rect y="8" width="8" height="8"/></svg>');
            border-radius: $radius-colorPicker-handle;
            border: $width-colorPicker_handle-border solid $color-colorPicker_handle-border;
            position: absolute;
            box-sizing: border-box;
            cursor: grab;

        }

    }

    &-colorSlider{
        position: relative;
        cursor: pointer;
        margin-top: $spacing-colorPicker_slider-marginTop;
        border-radius: $radius-colorPicker-handle;
        background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
    }



    &-dataPart{
        margin-top: $spacing-colorPicker_dataPart-marginTop;
        display: flex;
        align-items: center;
        .#{$module}-colorDemoBlock{
            border-radius: $radius-colorPicker-demoBlock;
        }
        .#{$module}-inputGroup{
            margin-left: $spacing-colorPicker_inputGroup-marginLeft;
            width: 100%;
            flex:1;
            flex-wrap: nowrap;
            .#{$module}-colorPickerInput{
                flex: 1;
            }
            .#{$module}-colorPickerInputNumber{
                width: $width-colorPicker-colorPickerInputNumber;
                .#{$module}-inputNumberSuffix{
                    font-size: $font-colorPicker_inputNumberSuffix-fontSize;
                    padding: $spacing-colorPicker_inputNumberSuffix-vertical $spacing-colorPicker_inputNumberSuffix-horizontal;
                }
            }
        }
        .#{$module}-formatSelect{
            width: $width-colorPicker-formatSelect;
        }
    }


    &-popover{
        padding: $spacing-colorPicker_popover-padding;

        &-defaultChildren{
            width:$width-colorPicker-defaultTrigger;
            height: $width-colorPicker-defaultTrigger;
            border-radius: $radius-colorPicker-defaultTrigger;
            cursor: pointer;
        }

    }



}
