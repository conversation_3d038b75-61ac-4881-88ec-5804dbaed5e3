import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { BasicTreeInnerData, BasicTreeProps, BasicTreeNodeData, BasicTreeNodeProps, BasicExpandedOtherProps } from '../tree/foundation';
import { Motion } from '../utils/type';
export type ValidateStatus = 'error' | 'warning' | 'default';
export type Size = 'small' | 'large' | 'default';
export type BasicRenderSelectedItemInMultiple = (treeNode: BasicTreeNodeData, otherProps: {
    index: number | string;
    onClose: (tagContent: any, e: any) => void;
}) => {
    isRenderInTag: boolean;
    content: any;
};
export type BasicRenderSelectedItemInSingle = (treeNode: BasicTreeNodeData) => any;
export type BasicRenderSelectedItem = BasicRenderSelectedItemInSingle | BasicRenderSelectedItemInMultiple;
export interface BasicTriggerRenderProps {
    [x: string]: any;
    componentProps: BasicTreeSelectProps;
    disabled: boolean;
    inputValue: string;
    placeholder: string;
    value: BasicTreeNodeData[];
    onClear: (e: any) => void;
    onSearch: (inputValue: string) => void;
    onRemove: (key: string) => void;
}
export type BasicOnChangeWithObject = (node: BasicTreeNodeData[] | BasicTreeNodeData, e: any) => void;
export type BasicOnChangeWithBasic = (value: BasicTreeNodeData['value'], node: BasicTreeNodeData[] | BasicTreeNodeData, e: any) => void;
export interface BasicOnChange {
    (node: BasicTreeNodeData[] | BasicTreeNodeData, e: any): void;
    (value: BasicTreeNodeData['value'] | Array<BasicTreeNodeData['value']>, node: BasicTreeNodeData[] | BasicTreeNodeData, e: any): void;
}
export interface BasicTreeSelectProps extends Pick<BasicTreeProps, 'virtualize' | 'renderFullLabel' | 'renderLabel' | 'autoExpandParent' | 'className' | 'defaultExpandAll' | 'defaultExpandedKeys' | 'defaultValue' | 'disabled' | 'emptyContent' | 'expandAction' | 'expandedKeys' | 'filterTreeNode' | 'labelEllipsis' | 'leafOnly' | 'multiple' | 'onChangeWithObject' | 'showClear' | 'showFilteredOnly' | 'style' | 'treeData' | 'treeNodeFilterProp' | 'value' | 'onExpand' | 'expandAll' | 'disableStrictly' | 'aria-label' | 'checkRelation' | 'preventScroll'> {
    borderless?: boolean;
    motion?: Motion;
    mouseEnterDelay?: number;
    mouseLeaveDelay?: number;
    arrowIcon?: any;
    autoAdjustOverflow?: boolean;
    clickToHide?: boolean;
    defaultOpen?: boolean;
    dropdownClassName?: string;
    dropdownMatchSelectWidth?: boolean;
    dropdownStyle?: any;
    insetLabel?: any;
    maxTagCount?: number;
    motionExpand?: boolean;
    optionListStyle?: any;
    outerBottomSlot?: any;
    outerTopSlot?: any;
    placeholder?: string;
    prefix?: any;
    searchAutoFocus?: boolean;
    searchPlaceholder?: string;
    showSearchClear?: boolean;
    size?: Size;
    suffix?: any;
    treeNodeLabelProp?: string;
    validateStatus?: ValidateStatus;
    zIndex?: number;
    searchPosition?: string;
    stopPropagation?: boolean | string;
    loadedKeys?: string[];
    showRestTagsPopover?: boolean;
    restTagsPopoverProps?: any;
    clickTriggerToHide?: boolean;
    loadData?: (data: BasicTreeNodeData) => Promise<void>;
    onSelect?: (selectedKey: string, selected: boolean, selectedNode: BasicTreeNodeData) => void;
    searchRender?: (inputProps: any) => any;
    renderSelectedItem?: BasicRenderSelectedItem;
    getPopupContainer?: () => HTMLElement;
    onBlur?: (e: any) => void;
    onSearch?: (sunInput: string, filteredExpandedKeys: string[], filteredNodes: BasicTreeNodeData[]) => void;
    onChange?: BasicOnChange;
    onFocus?: (e: any) => void;
    onVisibleChange?: (isVisible: boolean) => void;
    onLoad?: (keys: Set<string>, data: BasicTreeNodeData) => void;
}
export interface BasicTreeSelectInnerData extends Pick<BasicTreeInnerData, 'keyEntities' | 'treeData' | 'flattenNodes' | 'cachedFlattenNodes' | 'selectedKeys' | 'checkedKeys' | 'halfCheckedKeys' | 'motionKeys' | 'motionType' | 'expandedKeys' | 'filteredKeys' | 'filteredExpandedKeys' | 'filteredShownKeys' | 'cachedKeyValuePairs' | 'inputValue' | 'disabledKeys' | 'loadedKeys' | 'loadingKeys' | 'realCheckedKeys'> {
    inputTriggerFocus: boolean;
    isOpen: boolean;
    rePosKey: number;
    dropdownMinWidth: null | number;
    isHovering: boolean;
    prevProps: BasicTreeSelectProps;
}
export interface TreeSelectAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    updateInputValue: (value: string) => void;
    registerClickOutsideHandler: (cb: (e: any) => void) => void;
    unregisterClickOutsideHandler: () => void;
    rePositionDropdown: () => void;
    updateState: (states: Partial<BasicTreeSelectInnerData>) => void;
    notifySelect: (selectedKey: string, selected: boolean, selectedNode: BasicTreeNodeData) => void;
    notifySearch: (input: string, filteredExpandedKeys: string[], filteredNodes: BasicTreeNodeData[]) => void;
    cacheFlattenNodes: (bool: boolean) => void;
    openMenu: () => void;
    closeMenu: (cb?: () => void) => void;
    getTriggerWidth: () => boolean | number;
    setOptionWrapperWidth: (width: null | number) => void;
    notifyClear: (e: any) => void;
    notifyChange: BasicOnChangeWithBasic;
    notifyChangeWithObject: BasicOnChangeWithObject;
    notifyExpand: (expandedKeys: Set<string>, expandedOtherProps: BasicExpandedOtherProps) => void;
    notifyFocus: (e: any) => void;
    notifyBlur: (e: any) => void;
    toggleHovering: (bool: boolean) => void;
    notifyLoad: (newLoadedKeys: Set<string>, data: BasicTreeNodeData) => void;
    updateInputFocus: (bool: boolean) => void;
    updateLoadKeys: (data: BasicTreeNodeData, resolve: (value?: any) => void) => void;
    updateIsFocus: (bool: boolean) => void;
}
export default class TreeSelectFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<TreeSelectAdapter<P, S>, P, S> {
    constructor(adapter: TreeSelectAdapter<P, S>);
    init(): void;
    destroy(): void;
    _setDropdownWidth(): void;
    _isMultiple(): any;
    _isAnimated(): any;
    _isDisabled(treeNode?: BasicTreeNodeProps): any;
    _isExpandControlled(): any;
    _isSelectToClose(): boolean;
    _isLoadControlled(): any;
    _showFilteredOnly(): any;
    findDataForValue(findValue: string): any;
    constructDataForValue(value: string): {
        [x: number]: string;
    };
    getDataForKeyNotInKeyEntities(value: string): any;
    handleKeyDown: (e: any) => void;
    getTreeNodeProps(key: string): BasicTreeNodeProps;
    handleNodeLoad(loadedKeys: Set<string>, loadingKeys: Set<string>, data: BasicTreeNodeData, resolve: (value?: any) => void): {
        loadingKeys?: undefined;
    } | {
        loadingKeys: Set<string>;
    };
    focusInput(bool: boolean): void;
    _notifyMultipleChange(key: string[], e: any): void;
    _notifyChange(key: any, e: any): void;
    _registerClickOutsideHandler: () => void;
    clearInputValue: () => void;
    _notifyFocus(e: any): void;
    handleTriggerFocus(e: any): void;
    onClickSingleTriggerSearchItem: (e: any) => void;
    _notifyBlur(e: any): void;
    handlerTriggerBlur(e: any): void;
    toggleHoverState(bool: boolean): void;
    open(): void;
    close(e: any): void;
    handleClick(e: any): void;
    /**
     * A11y: simulate selection click
     */
    handleSelectionEnterPress(e: any): void;
    handleClear(e: any): void;
    /**
     * A11y: simulate clear button click
     */
    handleClearEnterPress(e: any): void;
    removeTag(eventKey: BasicTreeNodeData['key']): void;
    clearInput(): void;
    handleInputChange(sugInput: string): void;
    handleNodeSelect(e: any, treeNode: BasicTreeNodeProps): void;
    handleSingleSelect(e: any, treeNode: BasicTreeNodeProps): void;
    calcCheckedKeys(eventKey: BasicTreeNodeProps['eventKey'], targetStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<string>;
    };
    handleMultipleSelect(e: any, treeNode: BasicTreeNodeProps): void;
    calcNonDisabledCheckedKeys(eventKey: string, targetStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<any>;
    };
    calcCheckedStatus(targetStatus: boolean, eventKey: string): boolean;
    handleNodeExpandInSearch(e: any, treeNode: BasicTreeNodeProps): void;
    handleNodeExpand(e: any, treeNode: BasicTreeNodeProps): void;
    /**
     * The selected items that need to be displayed in the search box when obtaining a single selection
     */
    getRenderTextInSingle(): any;
    /**
     * When the search box is on the trigger, the blur event handling method
     */
    handleInputTriggerBlur(): void;
    /**
     * When the search box is on the trigger, the focus event processing method
     */
    handleInputTriggerFocus(): void;
    setLoadKeys(data: BasicTreeNodeData, resolve: (value?: any) => void): void;
    handlePopoverVisibleChange(isVisible: boolean): void;
    handleAfterClose(): void;
}
