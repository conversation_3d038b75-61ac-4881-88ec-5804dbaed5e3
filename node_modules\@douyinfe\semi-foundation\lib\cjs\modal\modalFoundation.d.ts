import BaseFoundation, { DefaultAdapter } from '../base/foundation';
export type OKType = 'primary' | 'secondary' | 'tertiary' | 'warning' | 'danger';
export type Size = 'small' | 'medium' | 'large' | 'full-width';
export interface ModalAdapter extends DefaultAdapter<ModalProps, ModalState> {
    disabledBodyScroll: () => void;
    enabledBodyScroll: () => void;
    notifyCancel: (e: any) => void | Promise<any>;
    notifyOk: (e: any) => void | Promise<any>;
    notifyClose: () => void;
    toggleDisplayNone: (displayNone: boolean, callback?: (displayNone: boolean) => void) => void;
    notifyFullScreen: (isFullScreen: boolean) => void;
    getProps: () => ModalProps;
}
export interface ModalProps {
    afterClose?: () => void;
    bodyStyle?: Record<string, any>;
    cancelButtonProps?: any;
    cancelText?: string;
    centered?: boolean;
    className?: string;
    modalContentClass?: string;
    closable?: boolean;
    confirmLoading?: boolean;
    cancelLoading?: boolean;
    content?: any;
    footer?: any;
    hasCancel?: boolean;
    header?: any;
    height?: string | number;
    mask?: boolean;
    maskClosable?: boolean;
    maskStyle?: Record<string, any>;
    maskFixed?: boolean;
    motion?: boolean;
    okButtonProps?: any;
    okText?: string;
    okType?: OKType;
    onCancel?: (e: any) => void | Promise<any>;
    onOk?: (e: any) => void | Promise<any>;
    style?: Record<string, any>;
    title?: any;
    visible?: boolean;
    width?: string | number;
    zIndex?: number;
    icon?: any;
    getPopupContainer?: () => HTMLElement;
    closeIcon?: any;
    closeOnEsc?: boolean;
    size?: Size;
    lazyRender?: boolean;
    keepDOM?: boolean;
    direction?: any;
    fullScreen?: boolean;
    preventScroll?: boolean;
    footerFill?: boolean;
}
export interface ModalState {
    displayNone: boolean;
    isFullScreen: boolean;
    onOKReturnPromiseStatus?: "pending" | "fulfilled" | "rejected";
    onCancelReturnPromiseStatus?: "pending" | "fulfilled" | "rejected";
}
export default class ModalFoundation extends BaseFoundation<ModalAdapter> {
    constructor(adapter: ModalAdapter);
    destroy(): void;
    handleCancel(e: any): void;
    handleOk(e: any): void;
    beforeShow(): void;
    afterHide(): void;
    enabledBodyScroll(): void;
    toggleDisplayNone: (displayNone: boolean, callback?: (displayNone: boolean) => void) => void;
}
