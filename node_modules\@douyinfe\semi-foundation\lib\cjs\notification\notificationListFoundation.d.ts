import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { NoticeInstance, NoticePosition, NoticeProps } from '../notification/notificationFoundation';
import { strings } from './constants';
export interface NotificationListProps {
}
export interface NotificationListState {
    notices: NoticeInstance[];
    removedItems: NoticeInstance[];
    updatedItems: NoticeInstance[];
}
export interface NotificationListAdapter extends DefaultAdapter<NotificationListProps, NotificationListState> {
    updateNotices: (notices: NoticeInstance[], removedItems?: NoticeInstance[], updatedItems?: NoticeInstance[]) => void;
    getNotices: () => NoticeInstance[];
}
export interface ConfigProps {
    top?: number | string;
    bottom?: number | string;
    left?: number | string;
    right?: number | string;
    duration?: number;
    position?: NoticePosition;
    zIndex?: number;
    direction?: typeof strings.directions[number];
}
export default class NotificationListFoundation extends BaseFoundation<NotificationListAdapter> {
    addNotice(opts: NoticeProps): void;
    has(id: string): boolean;
    update(id: string, noticeOpts: NoticeProps): void;
    removeNotice(id: string): void;
    destroyAll(): void;
}
