/* shadow */
/* sizing */
/* spacing */
.semi-button-split {
  display: inline-block;
}
.semi-button-split .semi-button {
  border-radius: 0;
  margin-right: 1px;
}
.semi-button-split .semi-button-first {
  border-top-left-radius: var(--semi-border-radius-small);
  border-bottom-left-radius: var(--semi-border-radius-small);
}
.semi-button-split .semi-button-last {
  border-top-right-radius: var(--semi-border-radius-small);
  border-bottom-right-radius: var(--semi-border-radius-small);
  margin-right: unset;
}
.semi-button-split:hover .semi-button-borderless:active {
  background-color: var(--semi-color-fill-1);
}

.semi-button {
  box-shadow: none;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  border: 0 transparent solid;
  border-radius: var(--semi-border-radius-small);
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  outline: none;
  vertical-align: middle;
  white-space: nowrap;
}
.semi-button.semi-button-primary:focus-visible, .semi-button.semi-button-secondary:focus-visible, .semi-button.semi-button-tertiary:focus-visible, .semi-button.semi-button-warning:focus-visible, .semi-button.semi-button-danger:focus-visible {
  outline: 2px solid var(--semi-color-primary-light-active);
}
.semi-button-content {
  display: flex;
  align-items: center;
}
.semi-button-danger {
  background-color: var(--semi-color-danger);
  color: rgba(var(--semi-white), 1);
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-danger-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-danger-disabled.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-danger-disabled.semi-button-light {
  background-color: var(--semi-color-fill-0);
}
.semi-button-danger:hover {
  background-color: var(--semi-color-danger-hover);
}
.semi-button-danger:active {
  background-color: var(--semi-color-danger-active);
}
.semi-button-danger.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-danger);
}
.semi-button-danger.semi-button-light, .semi-button-danger.semi-button-outline, .semi-button-danger.semi-button-borderless {
  color: var(--semi-color-danger);
}
.semi-button-danger:not(.semi-button-borderless):not(.semi-button-light):focus-visible {
  outline: 2px solid var(--semi-color-danger-light-active);
}
.semi-button-warning {
  background-color: var(--semi-color-warning);
  color: rgba(var(--semi-white), 1);
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-warning-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-warning-disabled.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-warning-disabled.semi-button-light {
  background-color: var(--semi-color-fill-0);
}
.semi-button-warning:hover {
  background-color: var(--semi-color-warning-hover);
}
.semi-button-warning:active {
  background-color: var(--semi-color-warning-active);
}
.semi-button-warning.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-warning);
}
.semi-button-warning.semi-button-light, .semi-button-warning.semi-button-outline, .semi-button-warning.semi-button-borderless {
  color: var(--semi-color-warning);
}
.semi-button-warning:not(.semi-button-borderless):not(.semi-button-light):focus-visible {
  outline: 2px solid var(--semi-color-warning-light-active);
}
.semi-button-tertiary {
  background-color: var(--semi-color-tertiary);
  color: rgba(var(--semi-white), 1);
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-tertiary-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-tertiary-disabled.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-tertiary-disabled.semi-button-light {
  background-color: var(--semi-color-fill-0);
}
.semi-button-tertiary:hover {
  background-color: var(--semi-color-tertiary-hover);
}
.semi-button-tertiary:active {
  background-color: var(--semi-color-tertiary-active);
}
.semi-button-tertiary.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-tertiary.semi-button-light, .semi-button-tertiary.semi-button-outline, .semi-button-tertiary.semi-button-borderless {
  color: var(--semi-color-text-1);
}
.semi-button-primary {
  background-color: var(--semi-color-primary);
  color: rgba(var(--semi-white), 1);
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-primary-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-primary-disabled.semi-button-light {
  background: var(--semi-color-fill-0);
}
.semi-button-primary-disabled.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-primary:not(.semi-button-borderless):not(.semi-button-light):not(.semi-button-outline):hover {
  background-color: var(--semi-color-primary-hover);
}
.semi-button-primary.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-primary:not(.semi-button-borderless):not(.semi-button-light):not(.semi-button-outline):active {
  background-color: var(--semi-color-primary-active);
}
.semi-button-primary.semi-button-light, .semi-button-primary.semi-button-outline, .semi-button-primary.semi-button-borderless {
  color: var(--semi-color-primary);
}
.semi-button-secondary {
  background-color: var(--semi-color-secondary);
  outline-color: var(--semi-color-secondary);
  color: rgba(var(--semi-white), 1);
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-secondary-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-secondary-disabled.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-secondary-disabled.semi-button-light {
  background-color: var(--semi-color-fill-0);
}
.semi-button-secondary.semi-button-outline {
  background-color: transparent;
  border: 1px solid var(--semi-color-border);
}
.semi-button-secondary:hover {
  background-color: var(--semi-color-secondary-hover);
}
.semi-button-secondary:active {
  background-color: var(--semi-color-secondary-active);
}
.semi-button-secondary.semi-button-light, .semi-button-secondary.semi-button-outline, .semi-button-secondary.semi-button-borderless {
  color: var(--semi-color-secondary);
}
.semi-button-disabled {
  color: var(--semi-color-disabled-text);
  cursor: not-allowed;
}
.semi-button-disabled:not(.semi-button-borderless):not(.semi-button-light):hover {
  color: var(--semi-color-disabled-text);
}
.semi-button-disabled.semi-button-light, .semi-button-disabled.semi-button-borderless {
  color: var(--semi-color-disabled-text);
}
.semi-button-borderless {
  background-color: transparent;
  border: 0 transparent solid;
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-borderless:not(.semi-button-disabled):hover {
  background-color: var(--semi-color-fill-0);
  border: 0 transparent solid;
}
.semi-button-borderless:not(.semi-button-disabled):active {
  background-color: var(--semi-color-fill-1);
  border: 0 transparent solid;
}
.semi-button-outline {
  background-color: transparent;
}
.semi-button-outline:not(.semi-button-disabled):hover {
  background-color: var(--semi-color-fill-0);
}
.semi-button-outline:not(.semi-button-disabled):active {
  background-color: var(--semi-color-fill-1);
}
.semi-button-light {
  background-color: var(--semi-color-fill-0);
  border: 0 transparent solid;
  transition: background-color var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none), border var(--semi-transition_duration-none) var(--semi-transition_function-easeIn) var(--semi-transition_delay-none);
  transform: scale(var(--semi-transform_scale-none));
}
.semi-button-light:not(.semi-button-disabled):hover {
  background-color: var(--semi-color-fill-1);
  border: 0 transparent solid;
}
.semi-button-light:not(.semi-button-disabled):active {
  background-color: var(--semi-color-fill-2);
  border: 0 transparent solid;
}
.semi-button-size-small {
  height: 24px;
  padding-top: 2px;
  padding-bottom: 2px;
  padding-left: 12px;
  padding-right: 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}
.semi-button-size-large {
  height: 40px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}
.semi-button-block {
  width: 100%;
}
.semi-button-group {
  display: flex;
  flex-wrap: wrap;
}
.semi-button-group > .semi-button {
  margin: 0;
  padding-left: 0;
  padding-right: 0;
  border-radius: 0;
}
.semi-button-group > .semi-button .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-button-group > .semi-button-size-large .semi-button-content {
  padding-left: 16px;
  padding-right: 16px;
}
.semi-button-group > .semi-button-size-small .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-button-group > .semi-button.semi-button-with-icon-only {
  padding-left: 0;
  padding-right: 0;
}
.semi-button-group > .semi-button.semi-button-with-icon-only .semi-button-content {
  padding-left: 8px;
  padding-right: 8px;
}
.semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-small .semi-button-content {
  padding-left: 4px;
  padding-right: 4px;
}
.semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-large .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-button-group > .semi-button:first-child {
  border-top-left-radius: var(--semi-border-radius-small);
  border-bottom-left-radius: var(--semi-border-radius-small);
}
.semi-button-group > .semi-button:last-child {
  border-top-right-radius: var(--semi-border-radius-small);
  border-bottom-right-radius: var(--semi-border-radius-small);
}
.semi-button-group > .semi-button-outline:not(:last-child) {
  border-right-color: transparent;
  margin-right: -1px;
}
.semi-button-group-line {
  display: inline-flex;
  align-items: center;
  background-color: var(--semi-color-border);
}
.semi-button-group-line-primary {
  background-color: var(--semi-color-primary);
}
.semi-button-group-line-secondary {
  background-color: var(--semi-color-secondary);
}
.semi-button-group-line-tertiary {
  background-color: var(--semi-color-tertiary);
}
.semi-button-group-line-warning {
  background-color: var(--semi-color-warning);
}
.semi-button-group-line-danger {
  background-color: var(--semi-color-danger);
}
.semi-button-group-line-disabled {
  background-color: var(--semi-color-disabled-bg);
}
.semi-button-group-line-light {
  background-color: var(--semi-color-fill-0);
}
.semi-button-group-line-borderless {
  background-color: transparent;
}
.semi-button-group-line::before {
  display: block;
  content: "";
  width: 1px;
  height: 20px;
  background-color: var(--semi-color-border);
}

.semi-rtl .semi-button,
.semi-portal-rtl .semi-button {
  direction: rtl;
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-size-small,
.semi-portal-rtl .semi-button-size-small {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-size-large,
.semi-portal-rtl .semi-button-size-large {
  padding-left: 16px;
  padding-right: 16px;
}
.semi-rtl .semi-button-group,
.semi-portal-rtl .semi-button-group {
  direction: rtl;
}
.semi-rtl .semi-button-group > .semi-button,
.semi-portal-rtl .semi-button-group > .semi-button {
  padding-left: 0;
  padding-right: 0;
}
.semi-rtl .semi-button-group > .semi-button .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-group > .semi-button-size-large .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button-size-large .semi-button-content {
  padding-left: 16px;
  padding-right: 16px;
}
.semi-rtl .semi-button-group > .semi-button-size-small .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button-size-small .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-group > .semi-button.semi-button-with-icon-only,
.semi-portal-rtl .semi-button-group > .semi-button.semi-button-with-icon-only {
  padding-left: 0;
  padding-right: 0;
}
.semi-rtl .semi-button-group > .semi-button.semi-button-with-icon-only .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button.semi-button-with-icon-only .semi-button-content {
  padding-left: 8px;
  padding-right: 8px;
}
.semi-rtl .semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-small .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-small .semi-button-content {
  padding-left: 4px;
  padding-right: 4px;
}
.semi-rtl .semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-large .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button.semi-button-with-icon-only.semi-button-size-large .semi-button-content {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-group > .semi-button:first-child,
.semi-portal-rtl .semi-button-group > .semi-button:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--semi-border-radius-small);
  border-bottom-right-radius: var(--semi-border-radius-small);
}
.semi-rtl .semi-button-group > .semi-button:not(:last-child) .semi-button-content,
.semi-portal-rtl .semi-button-group > .semi-button:not(:last-child) .semi-button-content {
  border-left: 1px var(--semi-color-border) solid;
  border-right: 0;
}
.semi-rtl .semi-button-group > .semi-button:last-child,
.semi-portal-rtl .semi-button-group > .semi-button:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--semi-border-radius-small);
  border-bottom-left-radius: var(--semi-border-radius-small);
}
.semi-rtl .semi-button.semi-button-with-icon-only,
.semi-portal-rtl .semi-button.semi-button-with-icon-only {
  padding-left: 8px;
  padding-right: 8px;
}
.semi-rtl .semi-button.semi-button-with-icon-only.semi-button-size-small,
.semi-portal-rtl .semi-button.semi-button-with-icon-only.semi-button-size-small {
  padding-left: 4px;
  padding-right: 4px;
}
.semi-rtl .semi-button.semi-button-with-icon-only.semi-button-size-large,
.semi-portal-rtl .semi-button.semi-button-with-icon-only.semi-button-size-large {
  padding-left: 12px;
  padding-right: 12px;
}
.semi-rtl .semi-button-content-left,
.semi-portal-rtl .semi-button-content-left {
  margin-left: 8px;
  margin-right: 0;
}
.semi-rtl .semi-button-content-right,
.semi-portal-rtl .semi-button-content-right {
  margin-right: 8px;
  margin-left: 0;
}