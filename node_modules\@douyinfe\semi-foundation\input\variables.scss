$color-input_default-border-default: transparent; // 输入框描边颜色 - 默认

$color-input_default-bg-default: var(--semi-color-fill-0); // 输入框背景颜色 - 默认
$color-input_default-text-default: var(--semi-color-text-0); // 输入框文本颜色 - 默认

$color-input_default-bg-hover: var(--semi-color-fill-1); // 输入框背景颜色 - 悬浮
$color-input_default-border-hover: transparent; // 输入框描边颜色 - 悬浮

$color-input_default-bg-active: var(--semi-color-fill-2); // 输入框背景颜色 - 按下
$color-input_default-border-active: $color-input_default-bg-active; // 输入框描边颜色 - 按下

$color-input_default-bg-focus: var(--semi-color-fill-0); // 输入框背景颜色 - 选中
$color-input_default-border-focus: var(--semi-color-focus-border); // 输入框描边颜色 - 选中
$color-input_icon-outline: var(--semi-color-primary-light-active); // 输入框 icon outline 颜色

$color-input_default-bg-focus-hover: $color-input_default-bg-focus; // 输入框背景颜色 - 选中悬浮

// error
$color-input_danger-bg-default: var(--semi-color-danger-light-default); // 错误输入框背景颜色 - 默认
$color-input_danger-border-default: var(--semi-color-danger-light-default); // 错误输入框描边颜色 - 默认

$color-input_danger-bg-hover: var(--semi-color-danger-light-hover); // 错误输入框背景颜色 - 悬浮
$color-input_danger-border-hover: var(--semi-color-danger-light-hover); // 错误输入框描边颜色 - 悬浮

$color-input_danger-bg-focus: var(--semi-color-danger-light-default); // 错误输入框背景颜色 - 选中
$color-input_danger-border-focus: var(--semi-color-danger); // 错误输入框描边颜色 - 选中

$color-input_danger-bg-active: var(--semi-color-danger-light-active); // 错误输入框背景颜色 - 按下
$color-input_danger-border-active: var(--semi-color-danger-light-active); // 错误输入框描边颜色 - 按下

// warning
$color-input_warning-bg-default: var(--semi-color-warning-light-default); // 警告输入框背景颜色 - 默认
$color-input_warning-border-default: var(--semi-color-warning-light-default); // 警告输入框文本颜色 - 默认

$color-input_warning-bg-hover: var(--semi-color-warning-light-hover); // 警告输入框背景颜色 - 悬浮
$color-input_warning-border-hover: var(--semi-color-warning-light-hover); // 警告输入框描边颜色 - 悬浮

$color-input_warning-bg-focus: var(--semi-color-warning-light-default); // 警告输入框背景颜色 - 选中
$color-input_warning-border-focus: var(--semi-color-warning); // 警告输入框描边颜色 - 选中

$color-input_warning-bg-active: var(--semi-color-warning-light-active); // 警告输入框背景颜色 - 按下
$color-input_warning-border-active: var(--semi-color-warning-light-active); // 警告输入框描边颜色 - 按下

// disabled
$color-input_disabled-border-default: var(--semi-color-disabled-border); // 禁用输入框描边颜色
$color-input_disabled-bg-default: var(--semi-color-disabled-fill); // 禁用输入框背景颜色
$color-input_disabled-text-default: var(--semi-color-disabled-text); // 禁用输入框文字颜色

$color-input_placeholder-text-default: var(--semi-color-text-2); // 输入框占位文字颜色
$color-input_prefix-text-default: var(--semi-color-text-2); // 输入框 prefix 文字颜色
$color-input-icon-default: var(--semi-color-text-2); // 输入框图标颜色
$color-input-icon-hover: var(--semi-color-primary-hover); // 输入框图标颜色 - 悬浮
$color-input-icon-active: var(--semi-color-primary-active); // 输入框图标颜色 - 按下

// prefix and suffix text

$color-input_counter-text-default: var(--semi-color-text-2); // 多行文本字数统计文字颜色 - 未超限
$color-input_counter_danger-text-default: var(--semi-color-danger); // 多行文本字数统计文字颜色 - 未超限

$color-input_group-border-default: var(--semi-color-border); // 输入框组合分割线颜色

$color_input-default-border-only_border-default: var(--semi-color-border); // 只有描边的输入框描边颜色 - 默认
$color_input-default-border-only_border-hover: var(--semi-color-border); // 只有描边的输入框描边颜色 - 默认

$height-input_large: $height-control-large - 2px; // 输入框高度 & 行高 - 大尺寸
$height-input_small: $height-control-small - 2px; // 输入框高度 & 行高 - 小尺寸
$height-input_default: $height-control-default - 2px; // 输入框高度 & 行高 - 默认尺寸
$height-input_wrapper_large: $height-control-large;
$height-input_wrapper_small: $height-control-small;
$height-input_wrapper_default: $height-control-default;

$width-input-icon: $width-icon-medium + $spacing-tight * 2; // 密码图标最小宽度
$width-input-icon_clear_before_suffix: $width-icon-medium + $spacing-tight; // suffix 之前的清除按钮宽度
$width-input-icon_clear_before_modebtn: $width-icon-medium; // 密码之前的清除按钮宽度

$motion-scale_size-active: .97;
$motion-scale_size-inactive: 1;

$width-input_append-border: $border-thickness-control; // 后置标签描边宽度
$width-input_prepend-border: $border-thickness-control; // 前置标签描边宽度
$width-input_group_pseudo-border: $border-thickness-control;
$width-input_wrapper-border: $border-thickness-control-focus; // 输入框描边宽度
$width-input_wrapper_focus-border: $border-thickness-control-focus; // 输入框描边宽度 - 选中态
$width-input_icon-outline: 2px; // 输入框 icon outline 宽度
$width-input_icon-outlineOffset: -1px; // 输入框 icon outline-offset 宽度

$radius-input_wrapper: var(--semi-border-radius-small); // 输入框圆角大小
$spacing-input_icon-marginLeft: -$spacing-base-tight; // 输入框图标左侧内边距
$spacing-input-paddingLeft: $spacing-base-tight; // 输入光标距离容器的左侧内边距
$spacing-input-paddingRight: $spacing-base-tight; // 输入文字距离容器的右侧内边距
$spacing-input_prefix_suffix-marginX: $spacing-base-tight; // prefix/suffix 水平外边距
$spacing-input_prefix_icon-marginY: 0; // prefix 图标垂直内边距
$spacing-input_prefix_icon-marginX: $spacing-tight; // prefix 图标水平内边距
$spacing-input_clearBtn_withSuffix-marginLeft: -$spacing-base-tight; // 清空按钮左侧内边距
$spacing-input_prepend-paddingY: 0; // 前置标签垂直内边距
$spacing-input_prepend-paddingX: $spacing-base-tight; // 前置标签水平内边距
$spacing-input_group_withTopLabel-marginTop: $spacing-base;
$spacing-input_group_withTopLabel-marginBottom: $spacing-base;
$font-input_prefix_suffix-fontWeight: $font-weight-bold; // prefix/suffix 文字字重

$spacing-textarea-paddingY: 5px; // 多行文本垂直内边距
$spacing-textarea-paddingX: $spacing-base-tight; // 多行文本水平内边距
$spacing-textarea_counter-paddingTop: 3px; // 多行文本字数统计顶部内边距
$spacing-textarea_counter-paddingRight: 12px; // 多行文本字数统计右侧内边距
$spacing-textarea_counter-paddingBottom: 5px; // 多行文本字数统计底部内边距
$spacing-textarea_counter-paddingLeft: 12px; // 多行文本字数统计左侧内边距

$width-textarea-border: $border-thickness; // 多行文本描边宽度
$height-textarea_counter: 24px; // 多行文本字数统计最小高度
$color-textarea-border-default: transparent; // 多行文本描边颜色
$color-textarea-border-hover: $color-textarea-border-default; // 多行文本描边颜色 - 悬浮

$color-textarea-icon-default: var(--semi-color-text-2); // 多行文本 clear 图标颜色
$color-textarea-icon-hover: var(--semi-color-primary-hover); // 多行文本 clear 图标颜色 - 悬浮
$width-textarea-icon: $width-icon-medium + $spacing-tight; // clear 图标最小宽度
$height-textarea-default: 32px; // 多行文本 clear 图标的高度
$spacing-textarea_withShowClear-paddingRight: 36px; // 多行文本设置 showClear 后的右内边距
$spacing-textarea-icon-right: $spacing-extra-tight;// 多行文本 clear 图标的右边距


