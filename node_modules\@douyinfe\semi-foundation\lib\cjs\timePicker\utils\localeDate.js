"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.localeFormat = localeFormat;
exports.localeParse = localeParse;
var _dateFns = require("date-fns");
/* istanbul ignore next */
const replace = function replace(str, replacements) {
  let _str = str;
  for (const key of Object.keys(replacements)) {
    if (typeof replacements[key] === 'string') {
      _str = _str.replace(key, replacements[key]);
    }
  }
  return _str;
};
/* istanbul ignore next */
function localeFormat(date, format) {
  let locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  let str = (0, _dateFns.format)(date, format, options);
  str = replace(str, locale);
  return str;
}
/* istanbul ignore next */
function localeParse(dateString, format) {
  let locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  let defaultDate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
  let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {};
  // const invertedLocale = invert(locale);
  const _dateString = replace(dateString, locale);
  return (0, _dateFns.parse)(_dateString, format, defaultDate, options);
}