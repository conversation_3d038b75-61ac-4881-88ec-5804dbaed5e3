declare const cssClasses: {
    PREFIX: string;
    ARROW: string;
};
declare const strings: {
    readonly POSITION_SET: readonly ["top", "topLeft", "topRight", "left", "leftTop", "leftBottom", "right", "rightTop", "rightBottom", "bottom", "bottomLeft", "bottomRight", "leftTopOver", "rightTopOver"];
    readonly TRIGGER_SET: readonly ["hover", "focus", "click", "custom", "contextMenu"];
    readonly DEFAULT_ARROW_STYLE: {
        readonly borderOpacity: "1";
        readonly backgroundColor: "var(--semi-color-bg-3)";
        readonly borderColor: "var(--semi-color-border)";
    };
};
declare const numbers: {
    ARROW_BOUNDING: {
        offsetY: number;
        offsetX: number;
        height: number;
        width: 24;
    };
    SPACING: number;
    SPACING_WITH_ARROW: number;
    DEFAULT_Z_INDEX: number;
};
export { cssClasses, strings, numbers };
