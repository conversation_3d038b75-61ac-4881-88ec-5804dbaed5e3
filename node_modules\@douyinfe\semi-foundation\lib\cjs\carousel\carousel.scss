@import './animation.scss';
@import './variables.scss';

$module: #{$prefix}-carousel;

.#{$module} {
    position: relative;
    overflow: hidden;

    &-content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        &-item {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;

            &-current {
                z-index: 1;
            }
        }

        &-fade {
            > * {
                opacity: 0;
            }

            .#{$module}-content-item-current {
                opacity: 1;
            }
        }

        &-slide {
            & > *:not(.#{$module}-content-item-current) {
                visibility: hidden;
            }

            .#{$module}-content-item-slide-out {
                display: block;
                animation: #{$module}-content-item-keyframe-slide-out;
                animation-fill-mode: forwards;
            }

            .#{$module}-content-item-slide-in {
                display: block;
                animation: #{$module}-content-item-keyframe-slide-in;
                animation-fill-mode: forwards;
            }
        }

        &-reverse {
            .#{$module}-content-item-slide-out {
                animation: #{$module}-content-item-keyframe-slide-out-reverse;
                animation-fill-mode: forwards;
            }

            .#{$module}-content-item-slide-in {
                animation: #{$module}-content-item-keyframe-slide-in-reverse;
                animation-fill-mode: forwards;
            }
        }
    }

    &-indicator {
        display: flex;
        align-items: flex-end;
        z-index: 2;

        &-left {
            position: absolute;
            left: $spacing-carousel_indicator-padding;
            bottom: $spacing-carousel_indicator-padding;
        }

        &-center {
            position: absolute;
            left: 50%;
            bottom: $spacing-carousel_indicator-padding;
            transform: translate(-50%);
        }

        &-right {
            position: absolute;
            right: $spacing-carousel_indicator-padding;
            bottom: $spacing-carousel_indicator-padding;
        }

        &-dot {
            .#{$module}-indicator-item {
                border-radius: $radius-carousel_indicator_dot;
                cursor: pointer;

                &:not(:last-child) {
                    margin-right: $spacing-carousel_indicator_dot-marginX;
                }

                &-small {
                    width: $width-carousel_indicator_dot_small;
                    height: $width-carousel_indicator_dot_small;
                }

                &-medium {
                    width: $width-carousel_indicator_dot_medium;
                    height: $width-carousel_indicator_dot_medium;
                }

                &-primary {
                    background-color: $color-carousel_indicator_theme_primary-bg-default;
                    transition: background-color $transition_duration-carousel_indicator-bg
                        $transition_function-carousel_indicator-bg $transition_delay-carousel_indicator-bg;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_primary-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }
                }

                &-light {
                    background-color: $color-carousel_indicator_theme_light-bg-default;
                    transition: background-color $transition_duration-carousel_indicator-bg
                        $transition_function-carousel_indicator-bg $transition_delay-carousel_indicator-bg;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_light-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }
                }

                &-dark {
                    background-color: $color-carousel_indicator_theme_dark-bg-default;
                    transition: background-color $transition_duration-carousel_indicator-bg
                        $transition_function-carousel_indicator-bg $transition_delay-carousel_indicator-bg;

                    &.#{$module}-indicator-item-active {
                        background-color: $color-carousel_indicator_theme_dark-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_dark-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_dark-bg-active;
                    }
                }
            }
        }

        &-line {
            width: $width-carousel_indicator_line;

            .#{$module}-indicator-item {
                flex: 1;
                cursor: pointer;

                &:not(:last-child) {
                    margin-right: $spacing-carousel_indicator_line-marginX;
                }

                &-small {
                    height: $height-carousel_indicator_line_small;
                }

                &-medium {
                    height: $height-carousel_indicator_line_medium;
                }

                &-primary {
                    background-color: $color-carousel_indicator_theme_primary-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_primary-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }
                }

                &-light {
                    background-color: $color-carousel_indicator_theme_light-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_light-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }
                }

                &-dark {
                    background-color: $color-carousel_indicator_theme_dark-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_dark-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_dark-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_dark-bg-active;
                    }
                }
            }
        }

        &-columnar {
            .#{$module}-indicator-item {
                cursor: pointer;

                &:not(:last-child) {
                    margin-right: $spacing-carousel_indicator_columnar-marginX;
                }

                &-small {
                    width: $width-carousel_indicator_columnar_small;
                    height: $height-carousel_indicator_columnar_small_default;

                    &.#{$module}-indicator-item-active {
                        height: $height-carousel_indicator_columnar_small_active;
                    }
                }

                &-medium {
                    width: $width-carousel_indicator_columnar_medium;
                    height: $height-carousel_indicator_columnar_medium_default;

                    &.#{$module}-indicator-item-active {
                        height: $height-carousel_indicator_columnar_medium_active;
                    }
                }

                &-primary {
                    background-color: $color-carousel_indicator_theme_primary-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_primary-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_primary-bg-active;
                    }
                }

                &-light {
                    background-color: $color-carousel_indicator_theme_light-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_light-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_light-bg-active;
                    }
                }

                &-dark {
                    background-color: $color-carousel_indicator_theme_dark-bg-default;

                    &.#{$module}-indicator-item-active {
                        background: $color-carousel_indicator_theme_dark-bg-active;
                    }

                    &:hover {
                        background-color: $color-carousel_indicator_theme_dark-bg-hover;
                    }

                    &:active {
                        background: $color-carousel_indicator_theme_dark-bg-active;
                    }
                }
            }
        }
    }

    &-arrow {
        display: flex;
        font-size: $width-carousel_arrow;
        cursor: pointer;

        &-prev {
            position: absolute;
            top: 50%;
            left: $spacing-carousel_arrow-left;
            transform: translateY(-50%);
            z-index: 2;
        }

        &-next {
            position: absolute;
            top: 50%;
            right: $spacing-carousel_arrow-right;
            transform: translateY(-50%);
            z-index: 2;
        }

        &-light {
            color: $color-carousel_arrow_theme_light-bg-default;
            transition: color $transition_duration-carousel_arrow-bg $transition_funciton_carousel_arrow-bg
                $transition_delay-carousel_arrow-bg;

            &:hover {
                color: $color-carousel_arrow_theme_light-bg-hover;
            }
        }

        &-primary {
            color: $color-carousel_arrow_theme_primary-bg-default;
            transition: color $transition_duration-carousel_arrow-bg $transition_funciton_carousel_arrow-bg
                $transition_delay-carousel_arrow-bg;

            &:hover {
                color: $color-carousel_arrow_theme_primary-bg-hover;
            }
        }

        &-dark {
            color: $color-carousel_arrow_theme_dark-bg-default;
            transition: color $transition_duration-carousel_arrow-bg $transition_funciton_carousel_arrow-bg
                $transition_delay-carousel_arrow-bg;

            &:hover {
                color: $color-carousel_arrow_theme_dark-bg-hover;
            }
        }
    }

    &-arrow-hover div {
        z-index: 2;
        opacity: 0;
    }

    &:hover {
        .#{$module}-arrow-hover div {
            opacity: 1;
        }
    }
}

@keyframes #{$module}-content-item-keyframe-slide-in {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes #{$module}-content-item-keyframe-slide-out {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(-100%);
    }
}

@keyframes #{$module}-content-item-keyframe-slide-in-reverse {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes #{$module}-content-item-keyframe-slide-out-reverse {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(100%);
    }
}

@import './rtl.scss';
