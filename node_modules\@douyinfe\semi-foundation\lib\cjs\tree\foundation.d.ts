/**
 * The drag and drop handler implementation is referenced from rc-tree
 * https://github.com/react-component/tree
 */
import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import type { KeyMapProps } from './treeUtil';
export type { KeyMapProps };
export interface BasicTreeNodeProps {
    [x: string]: any;
    expanded?: boolean;
    selected?: boolean;
    checked?: boolean;
    halfChecked?: boolean;
    active?: boolean;
    disabled?: boolean;
    loaded?: boolean;
    loading?: boolean;
    isLeaf?: boolean;
    pos?: string;
    children?: BasicTreeNodeData[];
    icon?: any;
    directory?: boolean;
    selectedKey?: string;
    motionKey?: string[] | string;
    eventKey?: string;
    showLine?: boolean;
}
export interface BasicTreeNodeData {
    [x: string]: any;
    key?: string;
    value?: number | string;
    label?: any;
    icon?: any;
    disabled?: boolean;
    isLeaf?: boolean;
    children?: BasicTreeNodeData[];
}
export interface BasicKeyEntities {
    [key: string]: BasicKeyEntity;
}
export interface BasicKeyEntity {
    children?: BasicKeyEntities;
    data?: BasicTreeNodeData;
    ind?: number;
    key?: string;
    level?: number;
    parent?: undefined | BasicKeyEntity;
    parentPos?: null | string;
    pos?: string;
}
export interface BasicDragTreeNode extends BasicTreeNodeData {
    expanded: boolean;
    /**
     * The positional relationship of the current node in the entire
     * treeData, such as the 0th node of the 2nd node of the 1st node
     * of the 0th layer: '0-1-2-0'
     */
    pos: string;
}
export interface BasicFlattenNode {
    _innerDataTag?: boolean;
    children?: BasicFlattenNode[];
    data?: BasicTreeNodeData;
    key?: string;
    label?: any;
    parent?: null | BasicFlattenNode;
    pos?: string;
    value?: string;
}
export interface BasicDragProps {
    event: any;
    node: BasicDragTreeNode;
}
export interface BasicDragEnterProps extends BasicDragProps {
    expandedKeys?: string[];
}
export type ExpandAction = false | 'click' | 'doubleClick';
export type BasicValue = string | number | BasicTreeNodeData | Array<BasicTreeNodeData | string | number>;
export interface BasicOnDragProps {
    event: any;
    node: BasicDragTreeNode;
    dragNode: BasicDragTreeNode;
    dragNodesKeys: string[];
    /**
     * dropPosition represents the position of the dragged node being
     * dropped in the current level. If inserted before the 0th node
     * of the same level, it is -1, after the 0th node, it is 1, and
     * it is 0 when it falls on it. And so on. With dropToGap, a more
     * complete judgment can be obtained.
     */
    dropPosition: number;
    /**
     * Indicates whether the dragged node is dropped between nodes, if
     *  it is false, it is dropped above a node
     */
    dropToGap: boolean;
}
export interface BasicRenderFullLabelProps {
    onClick: (e: any) => void;
    onContextMenu: (e: any) => void;
    onDoubleClick: (e: any) => void;
    className: string;
    onExpand: (e: any) => void;
    data: BasicTreeNodeData;
    level: number;
    style: any;
    onCheck: (e: any) => void;
    expandIcon: any;
    checkStatus: {
        checked: boolean;
        halfChecked: boolean;
    };
    expandStatus: {
        expanded: boolean;
        loading: boolean;
    };
    filtered: boolean | undefined;
    searchWord: string | undefined;
}
export interface BasicSearchRenderProps {
    className: string;
    placeholder: string;
    prefix: any;
    showClear?: boolean;
    value: string;
    onChange: (value: string) => void;
}
export interface TreeDataSimpleJson {
    [x: string]: string | TreeDataSimpleJson;
}
export interface Virtualize {
    itemSize: number;
    height?: number | string;
    width?: number | string;
}
export type CheckRelation = 'related' | 'unRelated';
export interface BasicTreeProps {
    autoExpandParent?: boolean;
    autoExpandWhenDragEnter?: boolean;
    blockNode?: boolean;
    children?: any;
    className?: string;
    expandAll?: boolean;
    defaultExpandAll?: boolean;
    defaultExpandedKeys?: string[];
    defaultValue?: BasicValue;
    directory?: boolean;
    disabled?: boolean;
    disableStrictly?: boolean;
    draggable?: boolean;
    emptyContent?: any;
    expandAction?: ExpandAction;
    expandedKeys?: string[];
    filterTreeNode?: boolean | ((inputValue: string, treeNodeString: string, data?: BasicTreeNodeData) => boolean);
    hideDraggingNode?: boolean;
    labelEllipsis?: boolean;
    leafOnly?: boolean;
    loadData?: (treeNode?: BasicTreeNodeData) => Promise<void>;
    loadedKeys?: string[];
    motion?: boolean;
    multiple?: boolean;
    onChange?: (value?: BasicValue) => void;
    onChangeWithObject?: boolean;
    onDoubleClick?: (e: any, node: BasicTreeNodeData) => void;
    onDragEnd?: (dragProps: BasicDragProps) => void;
    onDragEnter?: (dragEnterProps: BasicDragEnterProps) => void;
    onDragLeave?: (dragProps: BasicDragProps) => void;
    onDragOver?: (dragProps: BasicDragProps) => void;
    onDragStart?: (dragProps: BasicDragProps) => void;
    onDrop?: (onDragProps: BasicOnDragProps) => void;
    onExpand?: (expandedKeys: string[], expandedOtherProps: BasicExpandedOtherProps) => void;
    onLoad?: (loadedKeys?: Set<string>, treeNode?: BasicTreeNodeData) => void;
    onContextMenu?: (e: any, node: BasicTreeNodeData) => void;
    onSearch?: (sunInput: string, filteredExpandedKeys: string[]) => void;
    onSelect?: (selectedKey: string, selected: boolean, selectedNode: BasicTreeNodeData) => void;
    preventScroll?: boolean;
    renderDraggingNode?: (nodeInstance: HTMLElement, node: BasicTreeNodeData) => HTMLElement;
    renderFullLabel?: (renderFullLabelProps: BasicRenderFullLabelProps) => any;
    renderLabel?: (label?: any, treeNode?: BasicTreeNodeData) => any;
    searchClassName?: string;
    searchPlaceholder?: string;
    searchRender?: ((searchRenderProps: BasicSearchRenderProps) => any) | false;
    searchStyle?: any;
    showClear?: boolean;
    showFilteredOnly?: boolean;
    showLine?: boolean;
    style?: any;
    treeData?: BasicTreeNodeData[];
    treeDataSimpleJson?: TreeDataSimpleJson;
    treeNodeFilterProp?: string;
    value?: BasicValue;
    virtualize?: Virtualize;
    icon?: any;
    checkRelation?: CheckRelation;
    'aria-label'?: string;
}
export interface BasicTreeInnerData {
    inputValue: string;
    keyEntities: BasicKeyEntities;
    treeData: BasicTreeNodeData[];
    flattenNodes: BasicFlattenNode[];
    selectedKeys: string[];
    checkedKeys: Set<string>;
    halfCheckedKeys: Set<string>;
    realCheckedKeys: Set<string>;
    motionKeys: Set<string>;
    motionType: string;
    expandedKeys: Set<string>;
    filteredKeys: Set<string>;
    filteredExpandedKeys: Set<string>;
    filteredShownKeys: Set<string>;
    prevProps: null | BasicTreeProps;
    loadedKeys: Set<string>;
    loadingKeys: Set<string>;
    cachedFlattenNodes: BasicFlattenNode[] | undefined;
    cachedKeyValuePairs: {
        [x: string]: string;
    };
    disabledKeys: Set<string>;
    dragging: boolean;
    dragNodesKeys: Set<string>;
    dragOverNodeKey: string[] | string | null;
    dropPosition: number | null;
}
export interface BasicExpandedOtherProps {
    expanded: boolean;
    node: BasicTreeNodeData;
}
export interface TreeAdapter extends DefaultAdapter<BasicTreeProps, BasicTreeInnerData> {
    updateInputValue: (value: string) => void;
    focusInput: () => void;
    updateState: (states: Partial<BasicTreeInnerData>) => void;
    notifyExpand: (expandedKeys: Set<string>, { expanded, node }: BasicExpandedOtherProps) => void;
    notifySelect: (selectKey: string, bool: boolean, node: BasicTreeNodeData) => void;
    notifyChange: (value: BasicValue) => void;
    notifySearch: (input: string, filteredExpandedKeys: string[]) => void;
    notifyRightClick: (e: any, node: BasicTreeNodeData) => void;
    notifyDoubleClick: (e: any, node: BasicTreeNodeData) => void;
    cacheFlattenNodes: (bool: boolean) => void;
    setDragNode: (treeNode: any) => void;
}
export default class TreeFoundation extends BaseFoundation<TreeAdapter, BasicTreeProps, BasicTreeInnerData> {
    delayedDragEnterLogic: any;
    constructor(adapter: TreeAdapter);
    _isMultiple(): any;
    _isAnimated(): any;
    _isDisabled(treeNode?: BasicTreeNodeProps): any;
    _isExpandControlled(): boolean;
    _isLoadControlled(): boolean;
    _isFilterable(): boolean;
    _showFilteredOnly(): any;
    getTreeNodeProps(key: string): BasicTreeNodeProps;
    notifyJsonChange(key: string[] | string, e: any): void;
    constructDataForValue(value: string): {
        [x: number]: string;
    };
    findDataForValue(findValue: string): any;
    getDataForKeyNotInKeyEntities(value: string): any;
    notifyMultipleChange(key: string[], e: any): void;
    notifyChange(key: string[] | string, e: any): void;
    handleInputChange(sugInput: string): void;
    handleNodeSelect(e: any, treeNode: BasicTreeNodeProps): void;
    handleNodeRightClick(e: any, treeNode: BasicTreeNodeProps): void;
    handleNodeDoubleClick(e: any, treeNode: BasicTreeNodeProps): void;
    handleSingleSelect(e: any, treeNode: BasicTreeNodeProps): void;
    calcCheckedKeys(eventKey: string, targetStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<string>;
    };
    calcCheckedStatus(targetStatus: boolean, eventKey: string): boolean;
    calcNonDisabledCheckedKeys(eventKey: string, targetStatus: boolean): {
        checkedKeys: Set<string>;
        halfCheckedKeys: Set<any>;
    };
    handleMultipleSelect(e: any, treeNode: BasicTreeNodeProps): void;
    setExpandedStatus(treeNode: BasicTreeNodeProps): {
        expandedKeys: Set<string>;
        expanded: boolean;
        data: any;
    };
    handleNodeExpand(e: any, treeNode: BasicTreeNodeProps): void;
    handleNodeLoad(loadedKeys: Set<string>, loadingKeys: Set<string>, data: BasicTreeNodeData, resolve: (value?: any) => void): {
        loadingKeys?: undefined;
    } | {
        loadingKeys: Set<string>;
    };
    getDragEventNodeData(node: BasicTreeNodeData): any;
    triggerDragEvent(name: string, event: any, node: BasicTreeNodeData, extra?: {}): void;
    clearDragState: () => void;
    handleNodeDragStart(e: any, treeNode: BasicTreeNodeData): void;
    handleNodeDragEnter(e: any, treeNode: BasicTreeNodeData, dragNode: any): void;
    handleNodeDragOver(e: any, treeNode: BasicTreeNodeData, dragNode: any): void;
    handleNodeDragLeave(e: any, treeNode: BasicTreeNodeData): void;
    handleNodeDragEnd(e: any, treeNode: BasicTreeNodeData): void;
    handleNodeDrop(e: any, treeNode: BasicTreeNodeData, dragNode: any): void;
}
