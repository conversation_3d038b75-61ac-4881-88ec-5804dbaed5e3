import BaseFoundation, { DefaultAdapter } from '../base/foundation';
interface KeyboardAdapter<P = Record<string, any>, S = Record<string, any>> extends DefaultAdapter<P, S> {
    registerKeyDown: (callback: (event: any) => void) => void;
    unregisterKeyDown: (callback: (event: any) => void) => void;
    updateFocusIndex: (focusIndex: number) => void;
    notifyKeyDown: (e: any) => void;
}
export interface DataItem {
    [x: string]: any;
    value?: string | number;
    label?: any;
}
export interface StateOptionItem extends DataItem {
    show?: boolean;
    key?: string | number;
}
export type AutoCompleteData = Array<DataItem | string>;
export interface AutoCompleteAdapter<P = Record<string, any>, S = Record<string, any>> extends KeyboardAdapter<P, S> {
    getTriggerWidth: () => number | undefined;
    setOptionWrapperWidth: (width: number) => void;
    updateInputValue: (inputValue: string | number) => void;
    toggleListVisible: (isShow: boolean) => void;
    updateOptionList: (optionList: Array<StateOptionItem>) => void;
    updateScrollTop: (index: number) => void;
    updateSelection: (selection: Map<any, any>) => void;
    notifySearch: (inputValue: string) => void;
    notifyChange: (value: string | number) => void;
    notifySelect: (option: StateOptionItem | string | number) => void;
    notifyDropdownVisibleChange: (isVisible: boolean) => void;
    notifyClear: () => void;
    notifyFocus: (event?: any) => void;
    notifyBlur: (event?: any) => void;
    rePositionDropdown: () => void;
    persistEvent: (event: any) => void;
    registerClickOutsideHandler: (cb: (e: any) => void) => void;
    unregisterClickOutsideHandler: () => void;
}
declare class AutoCompleteFoundation<P = Record<string, any>, S = Record<string, any>> extends BaseFoundation<AutoCompleteAdapter<P, S>, P, S> {
    private _keydownHandler;
    constructor(adapter: AutoCompleteAdapter<P, S>);
    isPanelOpen: boolean;
    init(): void;
    destroy(): void;
    _setDropdownWidth(): void;
    handleInputClick(e?: MouseEvent): void;
    openDropdown(): void;
    closeDropdown(e?: any): void;
    _generateList(data: AutoCompleteData): Array<StateOptionItem>;
    handleSearch(inputValue: string): void;
    handleSelect(option: StateOptionItem, optionIndex?: number): void;
    updateSelection(option: StateOptionItem): void;
    notifySelect(option: StateOptionItem): void;
    _backwardLabelInValue(): any;
    handleDataChange(newData: any[]): void;
    handleValueChange(propValue: any): void;
    _modifyFocusIndex(searchValue: any): void;
    _modifyFocusIndexOnPanelOpen(): void;
    _getRenderSelectedItem(): any;
    handleClear(): void;
    bindKeyBoardEvent(): void;
    _handleKeyDown(event: KeyboardEvent): void;
    _getEnableFocusIndex(offset: number): void;
    _handleArrowKeyDown(offset: number): void;
    _handleEnterKeyDown(): void;
    handleOptionMouseEnter(optionIndex: number): void;
    handleFocus(e: FocusEvent): void;
    handleBlur(e: any): void;
}
export default AutoCompleteFoundation;
