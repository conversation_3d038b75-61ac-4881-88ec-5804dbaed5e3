declare const cssClasses: {
    PREFIX: string;
    PREFIX_OPTION: string;
};
declare const strings: {
    readonly SIZE_SET: readonly ["small", "large", "default"];
    readonly VALIDATE_STATUS: readonly ["success", "default", "error", "warning"];
    readonly IS_KEY: "isKey";
    readonly IS_VALUE: "isValue";
    readonly SHOW_NEXT_BY_CLICK: "click";
    readonly SHOW_NEXT_BY_HOVER: "hover";
    readonly LEAF_ONLY_MERGE_TYPE: "leafOnly";
    readonly AUTO_MERGE_VALUE_MERGE_TYPE: "autoMergeValue";
    readonly NONE_MERGE_TYPE: "none";
    readonly SEARCH_POSITION_TRIGGER: "trigger";
    readonly SEARCH_POSITION_CUSTOM: "custom";
    readonly RELATED: "related";
    readonly UN_RELATED: "unRelated";
};
declare const numbers: {};
export { cssClasses, strings, numbers };
export declare const VALUE_SPLIT = "_SEMI_CASCADER_SPLIT_";
