"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.transformToArray = exports.secondIsDisabled = exports.parseToTimestamp = exports.parseToDate = exports.minuteIsDisabled = exports.isTimeFormatLike = exports.hourIsDisabled = exports.formatToString = void 0;
var _toNumber2 = _interopRequireDefault(require("lodash/toNumber"));
var _dateFns = require("date-fns");
var _constants = require("../constants");
var _isNullOrUndefined = _interopRequireDefault(require("../../utils/isNullOrUndefined"));
var _locale = require("date-fns/locale");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
/**
 *
 * @param {string|Date|number} input
 * @param {string} formatToken
 * @param {object} dateFnsLocale
 * @returns {Date}
 */
const parseToDate = function (input) {
  let formatToken = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _constants.strings.DEFAULT_FORMAT;
  let dateFnsLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _locale.zhCN;
  if (input instanceof Date) {
    return input;
  } else if (typeof input === 'number') {
    return new Date((0, _toNumber2.default)(input));
  } else if (typeof input === 'string') {
    if (input === '') return undefined;
    let curDate = new Date();
    // console.log(input, formatToken);
    curDate = (0, _dateFns.parse)(input, formatToken, curDate, {
      locale: dateFnsLocale
    });
    // console.log(curDate, formatToken);
    return curDate;
  } else if (typeof input === 'undefined') {
    return undefined;
  }
  return new Date();
};
/**
 *
 * @param {string|Date|number} input
 * @returns {number}
 */
exports.parseToDate = parseToDate;
const parseToTimestamp = function (input) {
  let formatToken = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _constants.strings.DEFAULT_FORMAT;
  let dateFnsLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _locale.zhCN;
  return Number(parseToDate(input, formatToken, dateFnsLocale));
};
/**
 *
 * @param {Date|number} dateOrTimestamp
 * @param {string} formatToken
 * @returns {string}
 */
exports.parseToTimestamp = parseToTimestamp;
const formatToString = function (dateOrTimestamp) {
  let formatToken = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _constants.strings.DEFAULT_FORMAT;
  let dateFnsLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _locale.zhCN;
  return (0, _dateFns.format)(dateOrTimestamp, formatToken, {
    locale: dateFnsLocale
  });
};
exports.formatToString = formatToString;
const hourIsDisabled = (disabledHours, hour) => {
  if (typeof disabledHours === 'function') {
    const disabledOptions = disabledHours();
    if (Array.isArray(disabledOptions) && !(0, _isNullOrUndefined.default)(hour) && disabledOptions.some(v => (0, _toNumber2.default)(v) === (0, _toNumber2.default)(hour))) {
      return true;
    }
  }
  return false;
};
exports.hourIsDisabled = hourIsDisabled;
const minuteIsDisabled = (disabledMinutes, hour, minute) => {
  if (typeof disabledMinutes === 'function') {
    const disabledOptions = disabledMinutes(hour);
    if (Array.isArray(disabledOptions) && !(0, _isNullOrUndefined.default)(hour) && !(0, _isNullOrUndefined.default)(minute) && disabledOptions.some(v => (0, _toNumber2.default)(v) === (0, _toNumber2.default)(minute))) {
      return true;
    }
  }
  return false;
};
exports.minuteIsDisabled = minuteIsDisabled;
const secondIsDisabled = (disabledSeconds, hour, minute, second) => {
  if (typeof disabledSeconds === 'function') {
    const disabledOptions = disabledSeconds(hour, minute);
    if (Array.isArray(disabledOptions) && !(0, _isNullOrUndefined.default)(hour) && !(0, _isNullOrUndefined.default)(minute) && !(0, _isNullOrUndefined.default)(second) && disabledOptions.some(v => (0, _toNumber2.default)(v) === (0, _toNumber2.default)(second))) {
      return true;
    }
  }
  return false;
};
exports.secondIsDisabled = secondIsDisabled;
const transformToArray = value => {
  if (!Array.isArray(value)) {
    return [];
  } else {
    return [...value];
  }
};
/**
 * Determine whether the time length is the same as the format
 * e.g.
 *  format      | time      | return
 *  HH:mm       | 12:00     | true
 *  HH:mm:ss    | 12:00:00  | true
 *  yyyy HH:mm  | 2021 12:00| true
 *  HH          | 1         | false
 *  HH:mm       | 12:0      | false
 *  HH          | 1         | false
 *  HH:mm:ss    | 12:00:0   | false
 * @param {String} time  e.g. 12:0
 * @param {String} formatToken e.g. HH:mm
 * @returns {Boolean}
 */
exports.transformToArray = transformToArray;
const isTimeFormatLike = (time, formatToken) => {
  let isLike = true;
  const dateFnsSupportFormatCh = 'BDEGHKLMOPQRSTXYabcehimopqstuwxyz'; // dateFns support format character
  const formatSupportChReg = new RegExp(`[${dateFnsSupportFormatCh}]`, 'g');
  const formatNotSupportChReg = new RegExp(`[^${dateFnsSupportFormatCh}]`, 'g');
  const hmsReg = /[H|m|s]{1,2}/;
  const formatSplitted = formatToken.split(formatNotSupportChReg); // => ['HH', 'mm'];
  const timeSeparator = formatToken.replace(formatSupportChReg, ''); // => :
  const timeReg = new RegExp(`[${timeSeparator}]`, 'g'); // => /[:]/g
  const timeSplitted = time.split(timeReg); // => ['12', '0]
  if (formatSplitted.length !== timeSplitted.length) {
    isLike = false;
  } else {
    for (let i = 0, len = timeSplitted.length; i < len; i++) {
      const formatStr = formatSplitted[i];
      const timeStr = timeSplitted[i];
      // Returns false if the current character corresponds to minutes and seconds and the length is less than format
      // when i=1 => '0'.length < 'mm'.length
      if (hmsReg.test(formatStr) && timeStr.length < formatStr.length) {
        isLike = false;
        break;
      }
    }
  }
  return isLike;
};
exports.isTimeFormatLike = isTimeFormatLike;