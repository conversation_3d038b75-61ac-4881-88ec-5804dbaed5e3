"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.strings = exports.numbers = exports.cssClasses = void 0;
var _constants = require("../base/constants");
const MODE_HORIZONTAL = 'horizontal';
const MODE_VERTICAL = 'vertical';
const cssClasses = exports.cssClasses = {
  PREFIX: `${_constants.BASE_CLASS_PREFIX}-navigation`
};
const strings = exports.strings = {
  MODE: [MODE_VERTICAL, MODE_HORIZONTAL],
  MODE_VERTICAL,
  MODE_HORIZONTAL,
  ICON_POS_LEFT: 'left',
  ICON_POS_RIGHT: 'right',
  DEFAULT_LOGO_ICON_SIZE: 'extra-large',
  TOGGLE_ICON_LEFT: 'left',
  TOGGLE_ICON_RIGHT: 'right'
};
const numbers = exports.numbers = {
  DEFAULT_SUBNAV_MAX_HEIGHT: 999,
  DEFAULT_TOOLTIP_SHOW_DELAY: 0,
  DEFAULT_TOOLTIP_HIDE_DELAY: 100,
  DEFAULT_SUBNAV_OPEN_DELAY: 0,
  DEFAULT_SUBNAV_CLOSE_DELAY: 100 // ms
};