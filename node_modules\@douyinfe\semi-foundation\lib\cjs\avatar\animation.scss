$animation_duration-additionalBorder: 800ms; // 边框动画持续时间
$animation_duration-content: 1000ms; // 内容动画持续时间


$animation_opacity-additionalBorder-start: 1; // 边框动画起始透明度
$animation_opacity-additionalBorder-end: 0; // 边框动画结束透明度


$animation_width-additionalBorder-end:0; // 边框动画结束宽度


$animation_scale-additionalBorder-start: 1; // 边框动画起始缩放比例
$animation_scale-additionalBorder-end: 1.15; // 边框动画结束缩放比例

$animation_scale-content-start: 1; // 边框动画起始缩放比例
$animation_scale-content-middle: 0.9; // 边框动画中间态缩放比例
$animation_scale-content-end: 1; // 边框动画结束缩放比例


