/**
 * Part of the utils function implementation process reference
 * https://github.com/react-component/tree/blob/master/src/util.tsx
 */
export interface KeyEntities {
    [x: string]: any;
}
export interface TreeDataSimpleJson {
    [x: string]: string | TreeDataSimpleJson;
}
export interface NodeData {
    key: any;
    label: any;
    value: any;
    children?: any;
}
export interface KeyMapProps {
    key?: string;
    label?: string;
    value?: string;
    disabled?: string;
    children?: string;
    isLeaf?: string;
    icon?: string;
}
/**
 * Flat nest tree data into flatten list. This is used for virtual list render.
 * @param treeNodeList Origin data node list
 * @param expandedKeys
 * @param filteredShownKeys
 * need expanded keys, provides `true` means all expanded
 */
export declare function flattenTreeData(treeNodeList: any[], expandedKeys: Set<string>, keyMaps: KeyMapProps, filteredShownKeys?: boolean | Set<any>): any[];
export declare function convertJsonToData(treeJson: TreeDataSimpleJson): any[];
/**
 * Traverse all the data by `treeData`.
 */
export declare function traverseDataNodes(treeNodes: any[], callback: (data: any) => void, keyMaps: KeyMapProps): void;
export declare function convertDataToEntities(dataNodes: any[], keyMaps?: KeyMapProps): {
    posEntities: {};
    keyEntities: {};
    valueEntities: {};
};
export declare function findKeysForValues(valueList: any, valueEntities: any, isMultiple?: boolean): any;
export declare function findDescendantKeys(selectedKeys: string[], options: KeyEntities, self?: boolean): string[];
export declare function findChildKeys(keys: string[], options: any, omitKeys?: any[]): any[];
export declare function findLeafKeys(keys: string[], options: any): any[];
export declare function findSiblingKeys(selectedKeys: string[], options: any, self?: boolean): any[];
export declare function findAncestorKeys(selectedKeys: string[], options: any, self?: boolean): any[];
export declare function calcCheckedKeys(values: any, keyEntities: KeyEntities): {
    checkedKeys: Set<string>;
    halfCheckedKeys: Set<any>;
};
export declare function calcExpandedKeys(keyList: any[], keyEntities: KeyEntities, autoExpandParent?: boolean): Set<any>;
export declare function calcExpandedKeysForValues(value: any, keyEntities: KeyEntities, isMultiple: boolean, valueEntities: any): Set<any>;
export declare function calcMotionKeys(oldKeySet: Set<string>, newKeySet: Set<string>, keyEntities: KeyEntities): {
    motionType: string;
    motionKeys: string[];
};
/**
 * @returns whether option includes sugInput.
 * When filterTreeNode is a function,returns the result of filterTreeNode which called with (sugInput, target, option).
 */
export declare function filter(sugInput: string, option: any, filterTreeNode: any, filterProps: any): any;
export declare function normalizedArr(val: any): any[];
export declare function normalizeKeyList(keyList: any, keyEntities: KeyEntities, leafOnly?: boolean, flag?: boolean): string[];
export declare function getMotionKeys(eventKey: string, expandedKeys: Set<string>, keyEntities: KeyEntities): any[];
export declare function calcCheckedKeysForChecked(key: string, keyEntities: KeyEntities, checkedKeys: Set<string>, halfCheckedKeys: Set<string>): {
    checkedKeys: Set<string>;
    halfCheckedKeys: Set<string>;
};
export declare function calcCheckedKeysForUnchecked(key: string, keyEntities: KeyEntities, checkedKeys: Set<string>, halfCheckedKeys: Set<string>): {
    checkedKeys: Set<string>;
    halfCheckedKeys: Set<string>;
};
export declare function filterTreeData(info: any): {
    flattenNodes: any[];
    filteredKeys: Set<any>;
    filteredExpandedKeys: Set<any>;
    filteredShownKeys: Set<any>;
};
export declare function getValueOrKey(data: any, keyMaps?: KeyMapProps): any;
export declare function normalizeValue(value: any, withObject: boolean, keyMaps?: KeyMapProps): any;
export declare function updateKeys(keySet: Set<string> | string[], keyEntities: KeyEntities): string[];
export declare function calcDisabledKeys(keyEntities: KeyEntities, keyMaps?: KeyMapProps): Set<string>;
export declare function calcDropRelativePosition(event: any, treeNode: any): 1 | -1 | 0;
export declare function getDragNodesKeys(key: string, keyEntities: KeyEntities): string[];
export declare function calcDropActualPosition(pos: string, relativeDropPos: any): any;
