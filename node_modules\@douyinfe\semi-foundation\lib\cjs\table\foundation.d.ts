import { ArrayElement } from '../utils/type';
import { BaseCheckboxProps } from '../checkbox/checkboxFoundation';
import BaseFoundation, { DefaultAdapter } from '../base/foundation';
import { strings } from './constants';
export interface BaseColumnProps<RecordType> {
    align?: BaseAlign;
    children?: Array<BaseColumnProps<RecordType>>;
    className?: string;
    colSpan?: number;
    dataIndex?: string;
    defaultFilteredValue?: any[];
    defaultSortOrder?: BaseSortOrder;
    filterChildrenRecord?: boolean;
    filterDropdown?: any;
    filterDropdownProps?: Record<string, any>;
    filterDropdownVisible?: boolean;
    filterIcon?: any;
    filterMultiple?: boolean;
    filteredValue?: any[];
    filters?: BaseFilter[];
    fixed?: BaseFixed;
    key?: string | number;
    onCell?: BaseOnCell<RecordType>;
    onFilter?: BaseOnFilter<RecordType>;
    onFilterDropdownVisibleChange?: BaseOnFilterDropdownVisibleChange;
    onHeaderCell?: BaseOnHeaderCell<RecordType>;
    render?: (...args: any[]) => any;
    renderFilterDropdownItem?: (...args: any[]) => any;
    sortChildrenRecord?: boolean;
    sortOrder?: BaseSortOrder;
    sorter?: BaseSorter<RecordType>;
    title?: any;
    useFullRender?: boolean;
    width?: string | number;
    ellipsis?: BaseEllipsis;
}
export interface OnChangeExtra {
    changeType?: 'sorter' | 'filter' | 'pagination';
}
interface MouseDownTarget {
    targetName: string;
    className: string;
}
export interface TableAdapter<RecordType> extends DefaultAdapter {
    resetScrollY: () => void;
    setSelectedRowKeys: (selectedRowKeys: BaseRowKeyType[]) => void;
    setDisabledRowKeys: (disabledRowKeys: BaseRowKeyType[]) => void;
    setCurrentPage: (currentPage: number) => void;
    setPagination: (pagination: BasePagination) => void;
    setGroups: (groups: Map<string, RecordType[]>) => void;
    setDataSource: (dataSource: RecordType[]) => void;
    setExpandedRowKeys: (expandedRowKeys: BaseRowKeyType[]) => void;
    setQuery: (query?: BaseColumnProps<RecordType>) => void;
    setQueries: (queries: BaseColumnProps<RecordType>[]) => void;
    setFlattenData: (flattenData: RecordType[]) => void;
    setAllRowKeys: (allRowKeys: BaseRowKeyType[]) => void;
    setHoveredRowKey: (hoveredRowKey: BaseRowKeyType) => void;
    setCachedFilteredSortedDataSource: (filteredSortedDataSource: RecordType[]) => void;
    setCachedFilteredSortedRowKeys: (filteredSortedRowKeys: BaseRowKeyType[]) => void;
    getCurrentPage: () => number;
    getCurrentPageSize: () => number;
    getCachedFilteredSortedDataSource: () => RecordType[];
    getCachedFilteredSortedRowKeys: () => BaseRowKeyType[];
    getCachedFilteredSortedRowKeysSet: () => Set<BaseRowKeyType>;
    setAllDisabledRowKeys: (allDisabledRowKeys: BaseRowKeyType[]) => void;
    getAllDisabledRowKeys: () => BaseRowKeyType[];
    getAllDisabledRowKeysSet: () => Set<BaseRowKeyType>;
    notifyFilterDropdownVisibleChange: (visible: boolean, dataIndex: string) => void;
    notifyChange: (changeInfo: {
        pagination: BasePagination;
        filters: BaseChangeInfoFilter<RecordType>[];
        sorter: BaseChangeInfoSorter<RecordType>;
        extra: OnChangeExtra;
    }) => void;
    notifyExpand: (expanded?: boolean, record?: BaseIncludeGroupRecord<RecordType>, mouseEvent?: any) => void;
    notifyExpandedRowsChange: (expandedRows: BaseIncludeGroupRecord<RecordType>[]) => void;
    notifySelect: (record?: BaseIncludeGroupRecord<RecordType>, selected?: boolean, selectedRows?: BaseIncludeGroupRecord<RecordType>[], nativeEvent?: any) => void;
    notifySelectAll: (selected?: boolean, selectedRows?: BaseIncludeGroupRecord<RecordType>[], changedRows?: BaseIncludeGroupRecord<RecordType>[], e?: any) => void;
    notifySelectInvert: (record?: RecordType[], selected?: boolean, selectedRows?: BaseIncludeGroupRecord<RecordType>[], nativeEvent?: any) => void;
    notifySelectionChange: (selectedRowKeys: BaseRowKeyType[], selectedRows: BaseIncludeGroupRecord<RecordType>[]) => void;
    isAnyColumnFixed: (columns?: BaseColumnProps<RecordType>[]) => boolean;
    useFixedHeader: () => boolean;
    setHeadWidths: (headWidths: Array<BaseHeadWidth>, index?: number) => void;
    getHeadWidths: (index?: number) => number[];
    getCellWidths: (flattenedColumns: BaseColumnProps<RecordType>[], flattenedWidths?: BaseHeadWidth[], ignoreScrollBarKey?: boolean) => number[];
    mergedRowExpandable: (record: RecordType) => boolean;
    isAnyColumnUseFullRender: (columns: BaseColumnProps<RecordType>[]) => boolean;
    getNormalizeColumns: () => (columns: BaseColumnProps<RecordType>[], children: any) => BaseColumnProps<RecordType>[];
    getHandleColumns: () => (queries: BaseColumnProps<RecordType>[], cachedColumns: BaseColumnProps<RecordType>[]) => BaseColumnProps<RecordType>[];
    getMergePagination: () => (pagination: BasePagination) => BasePagination;
    setBodyHasScrollbar: (bodyHasScrollBar: boolean) => void;
    getTableLayout: () => 'fixed' | 'auto';
}
declare class TableFoundation<RecordType> extends BaseFoundation<TableAdapter<RecordType>> {
    memoizedWithFnsColumns: (queries: BaseColumnProps<RecordType>[], cachedColumns: BaseColumnProps<RecordType>[], rowSelectionUpdate: boolean, hideExpandedColumn: boolean, bodyHasScrollBar: boolean) => BaseColumnProps<RecordType>[];
    memoizedFilterColumns: (columns: BaseColumnProps<RecordType>[], ignoreKeys?: string[]) => BaseColumnProps<RecordType>[];
    memoizedFlattenFnsColumns: (columns: BaseColumnProps<RecordType>[], childrenColumnName?: string) => BaseColumnProps<RecordType>[];
    memoizedPagination: (pagination: BasePagination) => BasePagination;
    /**
     * update columns in place, and use default values as initial values if the sorting and filtering columns have no values
     */
    static initColumnsFilteredValueAndSorterOrder(columns: BaseColumnProps<unknown>[]): BaseColumnProps<unknown>[];
    /**
     * init filteredValue of filtering column, use defaultFilteredValue or [] when it is undefined
     */
    static initFilteredValue(column: BaseColumnProps<unknown>): void;
    /**
     * init sortOrder of sorting column, use defaultSortOrder or [] when it is undefined
     */
    static initSorterOrder(column: BaseColumnProps<unknown>): void;
    mouseDownTarget: MouseDownTarget;
    constructor(adapter: TableAdapter<RecordType>);
    init(): void;
    initExpandedRowKeys({ groups }?: {
        groups?: Map<string, RecordType[]>;
    }): void;
    initSelectedRowKeys({ disabledRowKeys }: {
        disabledRowKeys?: BaseRowKeyType[];
    }): void;
    /**
     * Get filtered and sorted data
     * @param {Object[]} dataSource
     * @param {Object[]} queries
     * @returns {Object[]} sortedDataSource
     */
    getFilteredSortedDataSource(dataSource: RecordType[], queries: BaseColumnProps<RecordType>[]): RecordType[];
    /**
     * get current page data
     *
     * @param {Array} dataSource
     * @param {object} pagination
     * @param {object} queries
     * @returns {{dataSource: RecordType[], groups: Map<string, Set<string>>, pagination: object, disabledRowKeys: string[], queries: BaseColumnProps[], allRowKeys: string[]}}
     */
    getCurrentPageData(dataSource?: RecordType[], pagination?: BasePagination, queries?: BaseColumnProps<RecordType>[]): BasePageData<RecordType>;
    /**
     * group dataSource, return grouped row keys
     *
     * @param {*[]} dataSource
     * @param {Function|string} groupBy
     */
    groupDataSource(dataSource: RecordType[], groupBy?: BaseGroupBy<RecordType>): {
        groups: Map<any, any>;
        dataSource: any[];
    };
    /**
     * sort data
     *
     * @param {Array} dataSource
     * @param {Array} sorters
     * @returns {Array}
     */
    sortDataSource(dataSource: RecordType[], sorters: BaseSorterInfo<RecordType>[]): RecordType[];
    /**
     * set page number
     */
    setPage: (currentPage: number, currentPageSize: number) => void;
    /**
     * filter data source
     *
     * @param {*[]} dataSource
     * @param {*[]} filters
     * @returns {*[]}
     */
    filterDataSource(dataSource: RecordType[], filters: BaseChangeInfoFilter<RecordType>[]): RecordType[];
    limitPageDataSource(dataSource: RecordType[], pagination: BasePagination): RecordType[];
    normalizePagination(pagination: BasePagination, dataSource: RecordType[]): BasePagination;
    setAdapterPageData(pageData?: BasePageData<RecordType>): void;
    /**
     * Cache related data when initializing or updating the calculated dataSource
     * @param {*} filteredSortedDataSource
     */
    setCachedFilteredSortedDataSource: (filteredSortedDataSource: RecordType[]) => void;
    destroy(): void;
    setAllDisabledRowKeys(disabledRowKeys: any): void;
    handleClick(e: any): void;
    handleMouseEnter(e: any): void;
    handleMouseLeave(e: any): void;
    stopPropagation(e: any): void;
    /**
     * Add non-repeating elements to the array itself
     */
    _addNoDuplicatedItemsToArr(srcArr?: any[], ...objArrs: any[][]): any[];
    _notifyChange(pagination: BasePagination, filters?: BaseChangeInfoFilter<RecordType>[], sorter?: BaseChangeInfoSorter<RecordType>, extra?: OnChangeExtra): void;
    _rowExpansionIsControlled(): boolean;
    _pagerIsControlled(): boolean;
    _selectionIsControlled(): boolean;
    /**
     * Determine whether the column sorting is controlled
     * Controlled: the column passed the sortOrder prop
     * @param {String} dataIndex
     * @returns {Boolean}
     */
    _sorterIsControlled(dataIndex: string): boolean;
    /**
     * Determine whether the column is filtered and controlled
     * Controlled: the column passed the filteredValue prop
     * @param {String} dataIndex
     * @returns {Boolean}
     */
    _filterIsControlled(dataIndex: string): boolean;
    _filterShowIsControlled(dataIndex?: string): boolean;
    _getSelectedRowKeys(): any[];
    _getSelectedRowKeysSet(): any;
    _getDataSource(): any;
    _getRecord(realKey: string | number): any;
    _getRecordChildren(record: RecordType): any;
    _getPagination(): any;
    /**
     * Filters are considered valid if filteredValue exists
     */
    _getAllFilters(queries?: BaseColumnProps<RecordType>[]): BaseChangeInfoFilter<RecordType>[];
    _getAllSorters(queries?: BaseColumnProps<RecordType>[]): BaseColumnProps<RecordType>[];
    _filterQueries(targetQuery: BaseColumnProps<RecordType>, queries: BaseColumnProps<RecordType>[], keys?: string[]): {
        filteredQueries: BaseColumnProps<RecordType>[];
        filteredIndexes: number[];
    };
    _mergeToQueries(query: BaseColumnProps<RecordType>, queries: BaseColumnProps<RecordType>[], keys?: string[]): BaseColumnProps<RecordType>[];
    /**
     * get record real key
     * @param {RecordType} record
     * @returns {string}
     */
    getRecordKey(record: RecordType): string;
    isEmpty(dataSource: RecordType[]): boolean;
    handleSelectRow(realKey: BaseRowKeyType, selected: boolean, e: any): void;
    /**
     * select all rows
     * @param {*} selected The future state of the select all button
     * @param {*} e
     */
    handleSelectAllRow(selected: boolean, e: any): void;
    /**
     * row keys => rows
     * @param {*} dataSource
     * @param {*} selectedRowKeys
     * @param {*} selectedRowKeysSet Recursive optimization
     */
    getSelectedRows(dataSource: RecordType[], selectedRowKeys: BaseRowKeyType[], selectedRowKeysSet?: Set<BaseRowKeyType>): BaseIncludeGroupRecord<RecordType>[];
    getAllDisabledRowKeys(dataSource?: RecordType[], getCheckboxProps?: GetCheckboxProps<RecordType>): BaseRowKeyType[];
    getAllRowKeys(dataSource: RecordType[]): BaseRowKeyType[];
    /**
     * Check if the selected item is in allRowKeysSet
     * @param {Array} selectedRowKeys
     * @param {Set} allRowKeysSet
     */
    hasRowSelected(selectedRowKeys: BaseRowKeyType[], allRowKeysSet: Set<BaseRowKeyType>): boolean;
    /**
     * expand processing function
     * @param {Boolean} expanded
     * @param {String} realKey
     * @param {Event} domEvent
     */
    handleRowExpanded(expanded: boolean, realKey: string, domEvent: any): void;
    /**
     * get state.groups
     * @returns {Map|Null}
     */
    _getGroups(): any;
    /**
     * Determine whether you have selected all except for disabled
     * @param {Set} selectedRowKeysSet
     * @param {Set} disabledRowKeysSet
     * @param {Array} allKeys keys after sorted and filtered
     */
    allIsSelected(selectedRowKeysSet: Set<BaseRowKeyType>, disabledRowKeysSet: Set<BaseRowKeyType>, allKeys: BaseRowKeyType[]): boolean;
    /**
     * This function is not used yet
     * @param {*} selectedRowKeys
     * @param {*} allKeys
     */
    allIsNotSelected(selectedRowKeys: BaseRowKeyType[], allKeys: BaseRowKeyType[]): boolean;
    formatPaginationInfo(pagination?: BasePagination, defaultPageText?: string): string;
    toggleShowFilter(dataIndex: string, visible: boolean): void;
    /**
     * Called when the filter changes
     * @param {*} dataIndex
     * @param {*} data
     */
    handleFilterSelect(dataIndex: string, data?: {
        filteredValue?: string[];
    }): void;
    /**
     * Click the sort button to call
     * @param {*} column
     * @param {*} e
     */
    handleSort(column: {
        dataIndex?: string;
        sortOrder?: BaseSortOrder;
    }, e: any, check?: boolean): void;
    handleMouseDown: (e: any) => void;
    /**
     * Recalculate the cached data after clicking filter or sorter
     * @param {*} queries
     */
    handleClickFilterOrSorter(queries: BaseColumnProps<RecordType>[]): void;
    getQuery(dataIndex: string, queries?: BaseColumnProps<RecordType>[]): BaseColumnProps<RecordType>;
    getCellWidths(flattenedColumns: BaseColumnProps<RecordType>[], flattenedWidths?: BaseHeadWidth[], ignoreScrollBarKey?: boolean): number[];
    setHeadWidths(headWidths: Array<BaseHeadWidth>, index?: number): void;
    getHeadWidths(index: number): number[];
    mergedRowExpandable(record: RecordType): boolean;
    setBodyHasScrollbar(bodyHasScrollbar: boolean): void;
    isSortOrderValid: (sortOrder: BaseSortOrder) => boolean;
}
export type BaseRowKeyType = string | number;
export interface BasePagination {
    total?: number;
    currentPage?: number;
    pageSize?: number;
    position?: ArrayElement<typeof strings.PAGINATION_POSITIONS>;
    defaultCurrentPage?: number;
    formatPageText?: any;
}
export interface BaseHeadWidth {
    width: number;
    key: string;
}
export interface BasePageData<RecordType> {
    dataSource?: RecordType[];
    groups?: Map<string, RecordType[]>;
    pagination?: BasePagination;
    disabledRowKeys?: BaseRowKeyType[];
    allRowKeys?: BaseRowKeyType[];
    queries?: BaseColumnProps<RecordType>[];
}
export type GetCheckboxProps<RecordType> = (record?: RecordType) => BaseCheckboxProps;
export type BaseGroupBy<RecordType> = string | number | BaseGroupByFn<RecordType>;
export type BaseGroupByFn<RecordType> = (record?: RecordType) => string | number;
export interface BaseSorterInfo<RecordType> {
    [x: string]: any;
    dataIndex?: string;
    sortOrder?: BaseSortOrder;
    sorter?: BaseSorter<RecordType>;
}
export type BaseSortOrder = boolean | ArrayElement<typeof strings.SORT_DIRECTIONS>;
export type BaseSorter<RecordType> = boolean | ((a?: RecordType, b?: RecordType, sortOrder?: 'ascend' | 'descend') => number);
export interface BaseChangeInfoFilter<RecordType> {
    dataIndex?: string;
    value?: any;
    text?: any;
    filters?: BaseFilter[];
    onFilter?: (filteredValue?: any, record?: RecordType) => boolean;
    filteredValue?: any[];
    defaultFilteredValue?: any[];
    children?: BaseFilter[];
    filterChildrenRecord?: boolean;
}
export interface BaseFilter {
    value?: any;
    text?: any;
    children?: BaseFilter[];
}
export type BaseFixed = ArrayElement<typeof strings.FIXED_SET>;
export type BaseAlign = ArrayElement<typeof strings.ALIGNS>;
export type BaseOnCell<RecordType> = (record?: RecordType, rowIndex?: number) => BaseOnCellReturnObject;
export interface BaseOnCellReturnObject {
    [x: string]: any;
    style?: Record<string, any>;
    className?: string;
    onClick?: (e: any) => void;
}
export type BaseOnFilter<RecordType> = (filteredValue?: any, record?: RecordType) => boolean;
export type BaseOnFilterDropdownVisibleChange = (visible?: boolean) => void;
export type BaseOnHeaderCell<RecordType> = (record?: RecordType, columnIndex?: number) => BaseOnHeaderCellReturnObject;
export interface BaseOnHeaderCellReturnObject {
    [x: string]: any;
    style?: Record<string, any>;
    className?: string;
    onClick?: (e: any) => void;
}
export interface BaseChangeInfoSorter<RecordType> {
    [x: string]: any;
    dataIndex: string;
    sortOrder: BaseSortOrder;
    sorter: BaseSorter<RecordType>;
}
export type BaseIncludeGroupRecord<RecordType> = RecordType | {
    groupKey: string;
};
export type BaseEllipsis = boolean | {
    showTitle: boolean;
};
export default TableFoundation;
