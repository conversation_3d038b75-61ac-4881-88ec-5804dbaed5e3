declare const cssClasses: {
    PREFIX: string;
    PREFIX_DIVIDER: string;
    PREFIX_CHAT_BOX: string;
    PREFIX_CHAT_BOX_ACTION: string;
    PREFIX_INPUT_BOX: string;
    PREFIX_ATTACHMENT: string;
    PREFIX_HINT: string;
};
declare const strings: {
    ROLE: {
        USER: string;
        ASSISTANT: string;
        SYSTEM: string;
        DIVIDER: string;
    };
    CHAT_ALIGN: {
        LEFT_RIGHT: string;
        LEFT_ALIGN: string;
    };
    MESSAGE_STATUS: {
        LOADING: string;
        INCOMPLETE: string;
        COMPLETE: string;
        ERROR: string;
    };
    PIC_SUFFIX_ARRAY: string[];
    PIC_PREFIX: string;
    SCROLL_ANIMATION_TIME: number;
    SHOW_SCROLL_GAP: number;
    MODE: {
        BUBBLE: string;
        NO_BUBBLE: string;
        USER_BUBBLE: string;
    };
    SEND_HOT_KEY: {
        ENTER: string;
        SHIFT_PLUS_ENTER: string;
    };
};
export { cssClasses, strings, };
