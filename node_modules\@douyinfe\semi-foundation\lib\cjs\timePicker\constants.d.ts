declare const cssClasses: {
    PREFIX: string;
    RANGE_PICKER: string;
    RANGE_PANEL_LISTS: string;
};
declare const strings: {
    TYPES: ("time" | "timeRange")[];
    TYPE_TIME_PICKER: "time";
    TYPE_TIME_RANGE_PICKER: "timeRange";
    DEFAULT_TYPE: "time";
    DEFAULT_RANGE_SEPARATOR: " ~ ";
    DEFAULT_MULTIPLE_SEPARATOR: ",";
    SIZE: readonly ["small", "large", "default"];
    DEFAULT_FORMAT: string;
    DEFAULT_FORMAT_A: string;
    STATUS: readonly ["default", "error", "warning", "success"];
    DEFAULT_POSITION: {
        time: string;
        timeRange: string;
    };
};
declare const numbers: {};
export { cssClasses, strings, numbers };
